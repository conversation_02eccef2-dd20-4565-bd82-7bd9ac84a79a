#!/usr/bin/env python
"""
Generate exp.original.txt file in AltAnalyze format from BED files
This script mimics AltAnalyze's exportJunctionCounts function
"""

import os
import sys

def parse_bed_files_for_expression(bed_dir):
    """Parse BED files and create expression matrix like AltAnalyze"""
    
    # Store junction data: coord_key -> {sample: reads}
    junction_expression = {}
    sample_names = []
    
    for filename in os.listdir(bed_dir):
        if filename.endswith('.bed') and ('junction' in filename.lower() or 'intron' in filename.lower()):
            filepath = os.path.join(bed_dir, filename)
            
            # Extract sample name (same logic as AltAnalyze)
            if '__junction.bed' in filename:
                sample_name = filename.replace('.Aligned.sortedByCoord.out__junction.bed', '.Aligned.sortedByCoord.out.bed')
            elif '__intronJunction.bed' in filename:
                sample_name = filename.replace('.Aligned.sortedByCoord.out__intronJunction.bed', '.Aligned.sortedByCoord.out.bed')
            else:
                sample_name = filename.replace('.bed', '.bed')
            
            if sample_name not in sample_names:
                sample_names.append(sample_name)
            
            print("Processing {}...".format(filename))
            
            with open(filepath, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('track') or line.startswith('#'):
                        continue
                    
                    parts = line.split('\t')
                    if len(parts) >= 12:
                        chr_name = parts[0]
                        exon1_start = int(parts[1])
                        exon2_stop = int(parts[2])
                        junction_id = parts[3]
                        reads = parts[4]
                        strand = parts[5]
                        lengths = parts[10]
                        
                        # Use AltAnalyze's exact coordinate calculation logic
                        exon1_len, exon2_len = map(int, lengths.split(',')[:2])
                        
                        if strand == '-':
                            if (exon1_len + exon2_len) == 0:
                                exon1_stop = exon1_start
                                exon2_start = exon2_stop
                            else:
                                exon1_stop = exon1_start + exon1_len
                                exon2_start = exon2_stop - exon2_len + 1
                            # Exons have opposite order for negative strand
                            a = (exon1_start, exon1_stop)
                            b = (exon2_start, exon2_stop)
                            exon1_stop, exon1_start = b
                            exon2_stop, exon2_start = a
                            final_coord = (chr_name, exon1_stop, exon2_start)
                        else:
                            if (exon1_len + exon2_len) == 0:
                                exon1_stop = exon1_start
                                exon2_start = exon2_stop
                            else:
                                exon1_stop = exon1_start + exon1_len
                                exon2_start = exon2_stop - exon2_len + 1
                            final_coord = (chr_name, exon1_stop, exon2_start)
                        
                        # Store expression data
                        if final_coord not in junction_expression:
                            junction_expression[final_coord] = {}
                        
                        # Combine reads if same sample has multiple entries for same junction
                        if sample_name in junction_expression[final_coord]:
                            existing_reads = int(junction_expression[final_coord][sample_name])
                            new_reads = int(reads)
                            junction_expression[final_coord][sample_name] = str(existing_reads + new_reads)
                        else:
                            junction_expression[final_coord][sample_name] = reads
    
    return junction_expression, sorted(sample_names)

def generate_altanalyze_ids(junction_expression):
    """Generate AltAnalyze-style IDs for junctions"""
    altanalyze_ids = {}
    
    for coord_key in junction_expression:
        chr_name, exon1_stop, exon2_start = coord_key
        
        # Create coordinate string (same as AltAnalyze)
        coordinates = "{}:{}-{}".format(chr_name, exon1_stop, exon2_start)
        
        # Generate a simple AltAnalyze-style ID
        # In real AltAnalyze, this would be based on gene annotation
        # For now, use a generic format
        altanalyze_id = "JUNCTION_{}:{}-{}".format(chr_name, exon1_stop, exon2_start)
        
        # Store the mapping
        altanalyze_ids[coord_key] = altanalyze_id + "=" + coordinates
    
    return altanalyze_ids

def export_expression_matrix(junction_expression, sample_names, altanalyze_ids, output_file):
    """Export expression matrix in AltAnalyze format"""
    
    print("Creating expression matrix: {}...".format(output_file))
    
    with open(output_file, 'w') as f:
        # Write header (same format as AltAnalyze)
        header = ['AltAnalyze_ID'] + sample_names
        f.write('\t'.join(header) + '\n')
        
        # Write data rows
        for coord_key in sorted(junction_expression.keys()):
            altanalyze_id = altanalyze_ids[coord_key]
            expression_data = junction_expression[coord_key]
            
            # Create row data
            row = [altanalyze_id]
            for sample in sample_names:
                if sample in expression_data:
                    row.append(expression_data[sample])
                else:
                    row.append('0')  # No reads for this sample
            
            f.write('\t'.join(row) + '\n')
    
    print("Expression matrix saved: {}".format(output_file))
    print("Total junctions: {}".format(len(junction_expression)))
    print("Total samples: {}".format(len(sample_names)))

def main():
    if len(sys.argv) != 3:
        print("Usage: python generate_exp_original.py <bed_directory> <output_file>")
        print("Example: python generate_exp_original.py bed_info exp.original.txt")
        sys.exit(1)
    
    bed_dir = sys.argv[1]
    output_file = sys.argv[2]
    
    if not os.path.exists(bed_dir):
        print("Error: BED directory not found: {}".format(bed_dir))
        sys.exit(1)
    
    print("=== Generating AltAnalyze-style Expression Matrix ===")
    print("BED directory: {}".format(bed_dir))
    print("Output file: {}".format(output_file))
    print()
    
    # Parse BED files
    junction_expression, sample_names = parse_bed_files_for_expression(bed_dir)
    
    # Generate AltAnalyze IDs
    altanalyze_ids = generate_altanalyze_ids(junction_expression)
    
    # Export expression matrix
    export_expression_matrix(junction_expression, sample_names, altanalyze_ids, output_file)
    
    print("\nDone!")

if __name__ == "__main__":
    main()
