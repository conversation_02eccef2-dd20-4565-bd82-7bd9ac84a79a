Filename	URL_type	ImportGroup	ConfigGroup	Key	Values	Comments
exon.txt	EnsemblSQL	Primary	Advanced	exon_id	seq_region_start|seq_region_end|stable_id|is_constitutive	used for creating the exon-transcript coordinate table and assigning genomic coordiantes to protein domains
exon_transcript.txt	EnsemblSQL	Primary	Advanced		exon_id|transcript_id|rank|stable_id	used for creating the exon-transcript coordinate table and assigning genomic coordiantes to protein domains
exon_stable_id.txt	EnsemblSQL	Primary	Advanced	exon_id	stable_id	
transcript.txt	EnsemblSQL	Primary	Basic	transcript_id	gene_id|stable_id|biotype	
transcript_stable_id.txt	EnsemblSQL	Primary	Basic	transcript_id	stable_id	
translation.txt	EnsemblSQL	Primary	Basic	translation_id	transcript_id|seq_start|start_exon_id|seq_end|end_exon_id|stable_id	sequence locations are relative to the corresponding exons (not genomic)
translation_stable_id.txt	EnsemblSQL	Primary	Basic	translation_id	stable_id	
gene.txt	EnsemblSQL	Primary	Basic	gene_id	biotype|seq_region_id|display_xref_id|seq_region_strand|description|stable_id	contains gene description (but not symbol). To get symbol lookup the `display_xref_id` in external_synonym
gene_stable_id.txt	EnsemblSQL	Primary	Basic	gene_id	stable_id	gene_id to Ensembl gene ID
protein_feature.txt	EnsemblSQL	Primary	Advanced	protein_feature_id	translation_id|seq_start|seq_end|hit_id|hit_name|evalue	hit_id is the domain accession which maps to 'id' in interpro
interpro.txt	EnsemblSQL	Primary	Advanced	id	interpro_ac	id = hit_id from protein_feature
external_db.txt	EnsemblSQL	Primary	Basic	external_db_id	db_name	internal ids for different gene systems
seq_region.txt	EnsemblSQL	Primary	Advanced	seq_region_id	name	relates to chromosomes ID
xref.txt	EnsemblSQL	Xref	Basic	xref_id	external_db_id|dbprimary_acc|display_label	"external gene IDs (names and IDs). Link to IDs of type ""external_db_id"" from the table external_db. Primary accession number (dbprimary_acc). The display_xref_id maps to a single entry in this file which is the gene symbol."
object_xref.txt	EnsemblSQL	Object-Xref	Basic	xref_id	xref_id|ensembl_id|ensembl_object_type	"links xref IDs to gene, transcript or protein IDs. `ensembl_id` (gene_id,transcript_id or protein_id) to xref. It appears that non-gene/transcript/protein IDs are not listed as  type of ID is specified by object_xref_id."
external_synonym.txt	EnsemblSQL	Secondary	Basic	xref_id	synonym	xref_id to secondary gene symbols (synonym)
homo_sapiens_core_49_36k.sql	EnsemblSQLDescriptions	Description	Basic			SQL description table - human should be applicable to any species
homo_sapiens_funcgen_49_36k.sql	EnsemblSQLFuncDescriptions	Description	FuncGen			SQL description table - human should be applicable to any species
xref.txt	EnsemblSQL	PrimaryFunc	FuncGen	xref_id	dbprimary_acc	xref_id is the Ensembl transcript ID (not the same as in core)
array.txt	EnsemblSQL	PrimaryFunc	FuncGen	array_id	name|vendor|format	
array_chip.txt	EnsemblSQL	PrimaryFunc	FuncGen	array_chip_id	array_id	
probe.txt	EnsemblSQL	PrimaryFunc	FuncGen	probe_id	probe_set_id|name|array_chip_id	
probe_set.txt	EnsemblSQL	PrimaryFunc	FuncGen	probe_set_id	name	
probe_feature.txt	EnsemblSQL	ProbeFeature	ProbeLevel	probe_id	seq_region_id|seq_region_start|seq_region_end|seq_region_strand	
seq_region.txt	EnsemblSQL	ProbeFeature	ProbeLevel	seq_region_id	name	relates to chromosomes ID
object_xref.txt	EnsemblSQL	Object-XrefFunc	FuncGen	ensembl_id	xref_id|ensembl_object_type	xref_id is the Ensembl transcript ID and ensembl_id is the probeset or probe ID