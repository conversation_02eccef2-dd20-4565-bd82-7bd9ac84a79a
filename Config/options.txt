Option	Mac-Displayed option	Display object	Group	Notes	Descriptions	Global defaults	exon	AltMouse	gene	3'array	junction	RNASeq	10XGenomics
dbase_version	Select database version	comboBox	ArrayType										
manufacturer_selection	Select vendor/data type	comboBox	ArrayType										
species	Select species	comboBox	ArrayType										
array_type	Select platform 	comboBox	ArrayType				---	---	---	---	---	---	---
update_dbs	"""Get new species/vendor/array databases online         """	single-checkbox	ArrayType				---	---	---	---	---	---	---
run_from_scratch	"""Analysis options          """	button	AnalysisType				Process CEL files|Process Expression file|Process AltAnalyze filtered|Annotate External Results|Interactive Result Viewer|Additional Analyses	Process CEL files|Process Expression file|Process AltAnalyze filtered|Interactive Result Viewer|Additional Analyses	Process CEL files|Process Expression file|Process AltAnalyze filtered|Annotate External Results|Interactive Result Viewer|Additional Analyses	Process CEL files|Process Expression file|Interactive Result Viewer|Additional Analyses	Process CEL files|Process Expression file|Process AltAnalyze filtered|Interactive Result Viewer|Additional Analyses	Process RNA-seq reads|Process Expression file|Process AltAnalyze filtered|Interactive Result Viewer|Additional Analyses	Process Chromium Matrix|Process Expression file|Interactive Result Viewer|Additional Analyses
selected_version	"""Select database version  """	comboBox	OnlineDatabases				---	---	---	---	---	---	---
selected_species1	"""Select species      """	comboBox	OnlineDatabases				---	---	---	---	---	---	---
selected_species2	"""Select species      """	comboBox	OnlineDatabases				---	---	---	---	---	---	---
selected_species3	"""Select species      """	comboBox	OnlineDatabases				---	---	---	---	---	---	---
update_goelite_resources	Download/update all gene-set analysis databases	single-checkbox	OnlineDatabases				yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
additional_analyses	"""Additional options           """	button	Additional Analyses				Pathway Enrichment|Pathway Visualization|Hierarchical Clustering|Dimensionality Reduction|Network Visualization|Identifier Translation|Cell Classification|AltExon Viewer|Venn Diagram|Merge Files	Pathway Enrichment|Pathway Visualization|Hierarchical Clustering|Dimensionality Reduction|Network Visualization|Identifier Translation|Cell Classification|AltExon Viewer|Venn Diagram|Merge Files	Pathway Enrichment|Pathway Visualization|Hierarchical Clustering|Dimensionality Reduction|Network Visualization|Identifier Translation|Cell Classification|AltExon Viewer|Venn Diagram|Merge Files	Pathway Enrichment|Pathway Visualization|Hierarchical Clustering|Dimensionality Reduction|Network Visualization|Identifier Translation|Cell Classification|AltExon Viewer|Venn Diagram|Merge Files	Pathway Enrichment|Pathway Visualization|Hierarchical Clustering|Dimensionality Reduction|Network Visualization|Identifier Translation|Cell Classification|AltExon Viewer|Venn Diagram|Merge Files	Pathway Enrichment|Pathway Visualization|Hierarchical Clustering|Dimensionality Reduction|Network Visualization|Identifier Translation|Cell Classification|AltExon Viewer|Venn Diagram|Merge Files	Pathway Enrichment|Pathway Visualization|Hierarchical Clustering|Dimensionality Reduction|Network Visualization|Identifier Translation|Cell Classification|AltExon Viewer|Venn Diagram|Merge Files
inputIDs	(optional) Analyze these gene symbols	enter	InputGOEliteDirs				---	---	---	---	---	---	---
criterion_input_folder	(optional) Select GO-Elite Input file directory	folder	InputGOEliteDirs				---	---	---	---	---	---	---
criterion_denom_folder	(optional) Select GO-Elite Denominator file directory	folder	InputGOEliteDirs				---	---	---	---	---	---	---
main_output_folder	Select GO-Elite output directory	folder	InputGOEliteDirs	"note: by default, the output will be stored in a set of new directories under\kthe same directory as the input ID folder."			---	---	---	---	---	---	---
new_run	Additional Options	button	AdditionalOptions				Perform GO/Pathway Analysis on Results|Change Parameters and Re-Run	Perform GO/Pathway Analysis on Results|Change Parameters and Re-Run	Perform GO/Pathway Analysis on Results|Change Parameters and Re-Run	Perform GO/Pathway Analysis on Results|Change Parameters and Re-Run	Perform GO/Pathway Analysis on Results|Change Parameters and Re-Run	Perform GO/Pathway Analysis on Results|Change Parameters and Re-Run	Perform GO/Pathway Analysis on Results|Change Parameters and Re-Run
dataset_name	Give a name to this dataset	enter	InputCELFiles				---	---	---	---	---	---	---
input_cel_dir	Select the CEL file containing folder	folder	InputCELFiles				---	---	---	---	---	---	---
input_fastq_dir	(optional) Select fastq files to run in Kallisto	folder	InputCELFiles				NA	NA	NA	NA	NA	---	NA
input_matrix_dir	"OR select a directory of .mtx, .h5 or .txt to integrate"	folder	InputCELFiles				NA	NA	NA	NA	NA	NA	---
output_CEL_dir	Select an AltAnalyze result output directory	folder	InputCELFiles				---	---	---	---	---	---	---
multithreading	Use multithreading for read genomic annotation	comboBox	InputCELFiles			no	NA	NA	NA	NA	NA	no|yes	NA
build_exon_bedfile	Build exon coordinate bed file to obtain BAM file exon counts\k(see the online tutorial for additional details and information)	single-checkbox	InputCELFiles				NA	NA	NA	NA	NA	NA	NA
useExonReads	Export exon bed files (will slow down analyses)	single-checkbox	InputCELFiles				NA	NA	NA	NA	NA	---	NA
channel_to_extract	Extract data from the following channels	comboBox	InputCELFiles				NA	NA	NA	green|red|green/red ratio|red/green ratio	NA	NA	NA
remove_xhyb	Remove probesets that have large cross-hybridization scores 	single-checkbox	InputCELFiles				---	---	NA	NA	NA	NA	NA
input_cdf_file	Select the PGF library file for your array (required)	file	InputLibraryFiles	note: the PGF file is apart of the standard library files for this array. This\kdirectory needs to also contain the CLF and BGP files for the array. These\kfiles can be downloaded from the Affymetrix website.			---	---	---	---	---	---	---
input_annotation_file	Select the CSV NetAffx annotation file for your array (recommended)	file	InputLibraryFiles	"note: the CSV annotation file should be listed under ""Current NetAffx\kannotations"" for this array."			---	---	---	---	---	---	---
input_exp_file	Select a probe set expression file (required)	file	InputExpFiles				---	---	---	---	---	---	---
input_stats_file	Select a probe set p-value file (optional)	file	InputExpFiles	"note: if not selected, an appropriate statistic file in the selected probe set\kexpression file directory will be used."			---	---	---	NA	---	NA	NA
output_dir	Select an AltAnalyze result output directory	folder	InputExpFiles	"note: by default, the output will be stored in a set of new directories under\kthe same directory as the input expression file.\k\kWhen analyzing already processed AltAnalyze outputs, select the file with\kthe prefix ""exp."" in the folder ""ExpressionInput""."			---	---	---	---	---	---	---
dabg_p	Remove probesets with a DABG p-value above 	enter	GeneExpression		Maximum average DABG p-value (applied to one or both of the compared biological groups) for ExpressionBuilder filtering.		---	---	---	NA	---	NA	NA
rpkm_threshold	Remove genes with an RPKM less than	enter	GeneExpression				NA	NA	NA	NA	NA	---	---
gene_exp_threshold	Remove genes expressed below (non-log) 	enter	GeneExpression		Maximum average non-log expression value (applied to one or both of the compared biological groups) for ExpressionBuilder filtering.		NA	NA	NA	NA	NA	---	NA
exon_exp_threshold	Remove exons expressed below (non-log) 	enter	GeneExpression		Maximum average non-log expression value (applied to one or both of the compared biological groups) for ExpressionBuilder filtering.		NA	NA	NA	NA	NA	---	NA
exon_rpkm_threshold	Remove exons with an RPKM less than	enter	GeneExpression				NA	NA	NA	NA	NA	---	NA
expression_threshold	Remove probesets expressed below (non-log) 	enter	GeneExpression		Maximum average non-log expression value (applied to one or both of the compared biological groups) for ExpressionBuilder filtering.		---	---	---	NA	---	---	NA
perform_alt_analysis	Perform alternative exon analysis	comboBox	GeneExpression		Indicates whether to just export the gene expression summary or in addition perform an alternative exon analysis.		yes|just expression	yes|just expression	yes|just expression	NA	yes|just expression	yes|just expression	NA
analyze_as_groups	Organize samples into groups	comboBox	GeneExpression		Indicates whether AltAnalyze should peform group comparisons or just export interim results and skip filtering steps. Will also use metaprobesets as opposed to agglomerating information from filtered probesets for gene expression calculation.		yes|only export sample data	NA	yes|only export sample data	NA	yes|only export sample data	yes|only export sample data	NA
expression_data_format	Expression data format 	comboBox	GeneExpression		Format the user data is in (typically log2 expression values).		scaled|counts	scaled|counts	scaled|counts	scaled|counts	scaled|counts	scaled|counts	scaled|counts
normalize_feature_exp	Normalize exon/junction expression	comboBox	GeneExpression				NA	NA	NA	NA	NA	RPKM|none	NA
normalize_gene_data	Normalize feature expression	comboBox	GeneExpression				NA	NA	NA	quantile|group|None	NA	NA	quantile|group|None
avg_all_for_ss	Determine gene expression levels using	comboBox	GeneExpression		"Indicates whether to average the expression all probesets, as opposed to constitutive only, to calculate a gene expression value for an associated gene."		constitutive probesets|core probesets	NA	constitutive probesets|core probesets	NA	constitutive probesets|known exons	constitutive exons|expressed exons|known exons|known junctions	NA
include_raw_data	Include replicate experiment values in export	comboBox	GeneExpression		"Whether or not to include replicate data in the ExpressionBuilder gene expression export file, as opposed to summary statistics (average, t-test p, folds, etc.)."		yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
probability_algorithm	Comparison group test statistic	comboBox	GeneExpression				moderated t-test|moderated Welch t-test|unpaired t-test|paired t-test|Kolmogorov Smirnov|Mann Whitney U|Rank Sums	moderated t-test|moderated Welch t-test|unpaired t-test|paired t-test|Kolmogorov Smirnov|Mann Whitney U|Rank Sums	moderated t-test|moderated Welch t-test|unpaired t-test|paired t-test|Kolmogorov Smirnov|Mann Whitney U|Rank Sums	moderated t-test|moderated Welch t-test|unpaired t-test|paired t-test|Kolmogorov Smirnov|Mann Whitney U|Rank Sums	moderated t-test|moderated Welch t-test|unpaired t-test|paired t-test|Kolmogorov Smirnov|Mann Whitney U|Rank Sums	moderated t-test|moderated Welch t-test|unpaired t-test|paired t-test|Kolmogorov Smirnov|Mann Whitney U|Rank Sums	moderated t-test|moderated Welch t-test|unpaired t-test|paired t-test|Kolmogorov Smirnov|Mann Whitney U|Rank Sums
FDR_statistic	False discovery rate statistic	comboBox	GeneExpression				Benjamini-Hochberg|q-value	Benjamini-Hochberg|q-value	Benjamini-Hochberg|q-value	Benjamini-Hochberg|q-value	Benjamini-Hochberg|q-value	Benjamini-Hochberg|q-value	Benjamini-Hochberg|q-value
batch_effects	Remove batch effects (Combat) 	comboBox	GeneExpression				yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
marker_finder	Predict condition-specific biomarkers	comboBox	GeneExpression				yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
visualize_results	Perform expression clustering and visual QC 	comboBox	GeneExpression				yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
run_lineage_profiler	Perform cell profiling with LineageProfiler	comboBox	GeneExpression				yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
run_goelite	Analyze ontologies and pathways with GO-Elite	comboBox	GeneExpression				run immediately|decide later	run immediately|decide later	run immediately|decide later	run immediately|decide later	run immediately|decide later	run immediately|decide later	run immediately|decide later
input_filtered_dir	Select a prior generated AltAnalyze results project folder	folder	InputFilteredFiles	note: all files in this directory should be built by AltAnalyze from a prior analysis.	"note: if not selected, all filtered probe set expression files in\k'AltExpression/<array_type>/<species>'\kwill be used and results written to 'AltResults/AlternativeOutput'.\k"		---	---	---	NA	---	---	NA
input_external_dir	Select a directory with lists of alternative probesets and scores.	folder	InputExternalFiles	"note: all files in this directory should contain probe set IDs and scores\k\k(e.g., FIRMA and MADS results). Files should have at least 3 columns\k(1) probe set ID, (2) normalized fold change and (3) splicing p-value."			---	---	---	NA	---	---	NA
analysis_method	Select the alternative exon algorithm	comboBox	AltAnalyze		Alternative exon analysis method to apply to user data.		splicing-index|FIRMA	ASPIRE|linearregres	splicing-index|FIRMA	NA	ASPIRE|linearregres|none	MultiPath-PSI|ASPIRE|linearregres|none	NA
additional_algorithms	Individual probeset analysis method 	comboBox	AltAnalyze				NA	NA	NA	NA	splicing-index|FIRMA|none	splicing-index|none	NA
filter_probe_types	Select probe sets to include	comboBox	AltAnalyze		Different options for which sets of probe sets to include in the alternative exon analysis.		core|extended|full	all|exons-only|junctions-only|combined-junctions	NA	NA	all|combined-junctions	all|combined-junctions	NA
analyze_all_conditions	Type of group comparisons to perform	comboBox	AltAnalyze		Different options for which sets of probe sets to include in the alternative exon analysis.		pairwise|all groups|both	pairwise|all groups|both	pairwise|all groups|both	NA	pairwise|all groups|both	pairwise|all groups|both	NA
p_threshold	Max MiDAS/normalized intensity p-value	enter	AltAnalyze		"Maximum user defined p-value to filter constitutive corrected alternative exon analysis t-test. For AltMouse analyses, only one of the two probe sets is required to meet this threshold."		---	---	---	NA	---	---	NA
alt_exon_fold_cutoff	Minimum alternative exon score	enter	AltAnalyze		Fold or ASPIRE minimal score for inclusion with results from the alternative exon analysis (non-log).		---	---	---	NA	---	---	NA
additional_score	Individual probeset min alt. exon score	enter	AltAnalyze		Fold or ASPIRE minimal score for inclusion with results from the alternative exon analysis (non-log).		NA	---	NA	NA	---	---	NA
permute_p_threshold	Maximum reciprocal junction p-value 	enter	AltAnalyze		Maximum allowed permutation p-value.		NA	---	NA	NA	---	---	NA
gene_expression_cutoff	Maximum absolute gene-expression change	enter	AltAnalyze		Absolute maximum gene-expression fold change allowed between pairwise-comparisons for alternative exon analysis (otherwise gene is excluded).		---	---	---	NA	---	---	NA
remove_intronic_junctions	Remove novel-intron splice junctions	comboBox	AltAnalyze		"Indicates whether to remove junctions with splice-site aligning to intron regions only (e.g., I1.1_1234-I1.1_1256)"		NA	NA	NA	NA	NA	yes|no	NA
perform_permutation_analysis	Perform permutation analysis	comboBox	AltAnalyze		Indicates whether to perform permutation analysis of ASPIRE or linearegress scores to calculate a statistical likelihood value.		NA	yes|no	NA	NA	yes|no	yes|no	NA
export_splice_index_values	Export all normalized intensities	single-checkbox	AltAnalyze		Export raw constitutive corrected probe set expression values for replicates. Useful for expression clustering of alternative exon changes.		yes|no	yes|no	yes|no	NA	yes|no	yes|no	NA
run_MiDAS	Calculate MiDAS p-values	single-checkbox	AltAnalyze		Export re-formatted input for analysis in MiDAS through an external application (Affymetrix Power Tools).		yes|no	NA	yes|no	NA	yes|no	yes|no	NA
calculate_splicing_index_p	Calculate normalized intensity p-values	single-checkbox	AltAnalyze				yes|no	NA	yes|no	NA	NA	NA	NA
filter_for_AS	Filter results for predicted AS	single-checkbox	AltAnalyze				yes|no	yes|no	yes|no	NA	yes|no	yes|no	NA
analyze_functional_attributes	Align probesets to protein domains using	comboBox	AltAnalyze				direct-alignment|inferred comparison	direct-alignment|inferred comparison	direct-alignment|inferred comparison	NA	direct-alignment|inferred comparison	direct-alignment|inferred comparison	NA
microRNA_prediction_method	# of agreeing miRNA databases needed	comboBox	AltAnalyze		Include binding sites only present in multiple analyzed micoRNA target databases or in at least one databases.		one|two or more	one|two or more	one|two or more	NA	one|two or more	one|two or more	NA
pick_filtering_options	Filter results by which algorithms	multiple-checkbox	Advanced				SI fold|SI p-value|MiDAS p-value	NA	NA	NA	NA	NA	NA
avg_all_for_ss_for_AS	Use known exons probesets\k to derive gene expression\k versus constitutive only	radio	Advanced				constitutive probesets|known exons	constitutive probesets|known exons	NA	NA	constitutive probesets|known exons	constitutive exons|known exons	NA
new_species_code	Two letter species code (e.g. Hs) 	enter	NewSpecies				---	---	---	---	---	---	---
new_species_name	Species name (e.g. Homo sapiens)	enter	NewSpecies				---	---	---	---	---	---	---
new_manufacturer	Choose vendor	comboBox	NewSpecies				---	---	---	---	---	---	---
allowed_array_systems	Choose array or data-type	comboBox	NewSpecies2				---	---	---	---	---	---	---
ge_fold_cutoffs	Minimum gene expression fold change	enter	GOElite				---	---	---	---	---	---	---
ge_pvalue_cutoffs	Maximum gene expression ttest p-value	enter	GOElite				---	---	---	---	---	---	---
ge_ptype	Filter based on the following p-value	comboBox	GOElite				rawp|adjp	rawp|adjp	rawp|adjp	rawp|adjp	rawp|adjp	rawp|adjp	rawp|adjp
filter_method	Prune Ontology terms using	comboBox	GOElite				z-score|gene number|combination	z-score|gene number|combination	z-score|gene number|combination	z-score|gene number|combination	z-score|gene number|combination	z-score|gene number|combination	z-score|gene number|combination
z_threshold	Z-score cutoff for initial filtering (> 0)	enter	GOElite				---	---	---	---	---	---	---
p_val_threshold	Enter permuted p-value cutoff (between 0-1)	enter	GOElite				---	---	---	---	---	---	---
change_threshold	Enter minimum number of changed genes	enter	GOElite				---	---	---	---	---	---	---
ORA_algorithm	Select the algorithm to use for ORA	comboBox	GOElite				Permute p-value|Fisher Exact Test	Permute p-value|Fisher Exact Test	Permute p-value|Fisher Exact Test	Permute p-value|Fisher Exact Test	Permute p-value|Fisher Exact Test	Permute p-value|Fisher Exact Test	Permute p-value|Fisher Exact Test
resources_to_analyze	Only analyze the following resource(s)	comboBox	GOElite				all|Pathways|Gene Ontology	all|Pathways|Gene Ontology	all|Pathways|Gene Ontology	all|Pathways|Gene Ontology	all|Pathways|Gene Ontology	all|Pathways|Gene Ontology	all|Pathways|Gene Ontology
pathway_permutations	Number of permutations for ORA	enter	GOElite				---	---	---	---	---	---	---
mod	Select primary relational gene system	comboBox	GOElite				Ensembl	EntrezGene	Ensembl	Ensembl|EntrezGene|HMDB	Ensembl	Ensembl	Ensembl|EntrezGene|HMDB
returnPathways	Visualize all over-represented WikiPathways	comboBox	GOElite	note: will add ~1 minute per pathway to be visualized			yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
get_additional	Download/update additional resources 	comboBox	GOElite				None	None	None	None	None	None	None
inputIDs	(optional) Analyze these gene symbols	enter	InputGOEliteFiles				---	---	---	---	---	---	---
elite_input_dir	(optional) Select a directory of GO-Elite formatted input file(s).	folder	InputGOEliteFiles				---	---	---	---	---	---	---
elite_denom_dir	(optional) Select a directory of GO-Elite formatted denominator file(s).	folder	InputGOEliteFiles				---	---	---	---	---	---	---
elite_output_dir	Select an GO-Elite output directory	folder	InputGOEliteFiles	"note: by default, the output will be stored in a set of new directories under\kthe same directory as the input expression file."			---	---	---	---	---	---	---
input_cluster_file	Select the tab-delimited expression file for clustering	file	heatmap	note: the expression file must have an annotation row and annotation column.\k Log2 values recommended. Results saved to the folder 'DataPlots'.\k			---	---	---	---	---	---	---
column_metric	Select the column clustering metric	comboBox	heatmap		http://docs.scipy.org/doc/scipy/reference/spatial.distance.html	cosine	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule
column_method	Select the column clustering method	comboBox	heatmap		http://docs.scipy.org/doc/scipy/reference/cluster.hierarchy.html	ward	average|single|complete|weighted|ward|hopach	average|single|complete|weighted|ward|hopach	average|single|complete|weighted|ward|hopach	average|single|complete|weighted|ward|hopach	average|single|complete|weighted|ward|hopach	average|single|complete|weighted|ward|hopach	average|single|complete|weighted|ward|hopach
row_metric	Select the row clustering metric	comboBox	heatmap		http://docs.scipy.org/doc/scipy/reference/spatial.distance.html	correlation	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule
row_method	Select the row clustering method	comboBox	heatmap		http://docs.scipy.org/doc/scipy/reference/cluster.hierarchy.html	ward	average|single|complete|weighted|ward|hopach	average|single|complete|weighted|ward|hopach	average|single|complete|weighted|ward|hopach	average|single|complete|weighted|ward|hopach	average|single|complete|weighted|ward|hopach	average|single|complete|weighted|ward|hopach	average|single|complete|weighted|ward|hopach
color_selection	Choose a color scheme	comboBox	heatmap	note: colors are indicated as up-null-down	http://matplotlib.sourceforge.net/examples/pylab_examples/show_colormaps.html	yellow-black-blue	red-white-blue|red-black-sky|red-black-blue|red-black-green|yellow-black-blue|yellow-black|green-white-purple|coolwarm|seismic|yellow_orange_red|Spectral|Greys	red-white-blue|red-black-sky|red-black-blue|red-black-green|yellow-black-blue|yellow-black|green-white-purple|coolwarm|seismic|yellow_orange_red|Spectral|Greys	red-white-blue|red-black-sky|red-black-blue|red-black-green|yellow-black-blue|yellow-black|green-white-purple|coolwarm|seismic|yellow_orange_red|Spectral|Greys	red-white-blue|red-black-sky|red-black-blue|red-black-green|yellow-black-blue|yellow-black|green-white-purple|coolwarm|seismic|yellow_orange_red|Spectral|Greys	red-white-blue|red-black-sky|red-black-blue|red-black-green|yellow-black-blue|yellow-black|green-white-purple|coolwarm|seismic|yellow_orange_red|Spectral|Greys	red-white-blue|red-black-sky|red-black-blue|red-black-green|yellow-black-blue|yellow-black|green-white-purple|coolwarm|seismic|yellow_orange_red|Spectral|Greys	red-white-blue|red-black-sky|red-black-blue|red-black-green|yellow-black-blue|yellow-black|green-white-purple|coolwarm|seismic|yellow_orange_red|Spectral|Greys
cluster_rows	Cluster rows	comboBox	heatmap			yes	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
cluster_columns	Cluster columns	comboBox	heatmap			yes	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
normalization	Normalize rows relative to	comboBox	heatmap			row median	NA|row mean|row median	NA|row mean|row median	NA|row mean|row median	NA|row mean|row median	NA|row mean|row median	NA|row mean|row median	NA|row mean|row median
transpose	Transpose matrix	comboBox	heatmap			no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
contrast	Heatmap color contrast level (scaling factor)	enter	heatmap				5	5	5	5	5	5	5
GeneSetSelection	(optional) Select GeneSet/Ontology to filter	comboBox	heatmap										
PathwaySelection	(optional) Select a specific GeneSet	multiple-comboBox	heatmap										
OntologyID	(optional) Type a pathway ID or Ontology ID	enter	heatmap				---	---	---	---	---	---	---
GeneSelection	(optional) Type a gene to get top correlated	enter	heatmap				---	---	---	---	---	---	---
JustShowTheseIDs	(optional) Display only selected gene IDs	enter	heatmap				---	---	---	---	---	---	---
CorrelationCutoff	(optional) Get all correlated genes with rho >	enter	heatmap				---	---	---	---	---	---	---
HeatmapAdvanced	Additional correlation options	multiple-comboBox	heatmap				None Selected|Exclude Cell Cycle Effects|Top Correlated Only|Positive Correlations Only|Intra-Correlated Only|Perform Iterative Discovery|Correlation Only to Guides|Perform Monocle	None Selected|Exclude Cell Cycle Effects|Top Correlated Only|Positive Correlations Only|Intra-Correlated Only|Perform Iterative Discovery|Correlation Only to Guides|Perform Monocle	None Selected|Exclude Cell Cycle Effects|Top Correlated Only|Positive Correlations Only|Intra-Correlated Only|Perform Iterative Discovery|Correlation Only to Guides|Perform Monocle	None Selected|Exclude Cell Cycle Effects|Top Correlated Only|Positive Correlations Only|Intra-Correlated Only|Perform Iterative Discovery|Correlation Only to Guides|Perform Monocle	None Selected|Exclude Cell Cycle Effects|Top Correlated Only|Positive Correlations Only|Intra-Correlated Only|Perform Iterative Discovery|Correlation Only to Guides|Perform Monocle	None Selected|Exclude Cell Cycle Effects|Top Correlated Only|Positive Correlations Only|Intra-Correlated Only|Perform Iterative Discovery|Correlation Only to Guides|Perform Monocle	None Selected|Exclude Cell Cycle Effects|Top Correlated Only|Positive Correlations Only|Intra-Correlated Only|Perform Iterative Discovery|Correlation Only to Guides|Perform Monocle
ClusterGOElite	(optional) Perform GeneSet cluster enrichment	comboBox	heatmap										
heatmapGeneSets	(optional) Store filtered genes for later	enter	heatmap				---	---	---	---	---	---	---
input_cluster_file	Select the tab-delimited expression file for clustering	file	PCA	note: the expression file must have an annotation row and annotation column.\k Log2 values recommended. Results saved to the folder 'DataPlots'.\k			---	---	---	---	---	---	---
pca_labels	Display sample labels next to each object	comboBox	PCA			no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
transpose	Transpose matrix	comboBox	PCA			no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
zscore	Z-score normalize	comboBox	PCA			no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
reimportModelScores	Re-import prior t-SNE plot coordinates	comboBox	PCA			yes	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
pca_algorithm	Algorithm to use	comboBox	PCA			UMAP	SVD|Eigen Vectors|t-SNE|SPRING|UMAP	SVD|Eigen Vectors|t-SNE|SPRING|UMAP	SVD|Eigen Vectors|t-SNE|SPRING|UMAP	SVD|Eigen Vectors|t-SNE|SPRING|UMAP	SVD|Eigen Vectors|t-SNE|SPRING|UMAP	SVD|Eigen Vectors|t-SNE|SPRING|UMAP	SVD|Eigen Vectors|t-SNE|SPRING|UMAP
dimensions	Dimensions to display	comboBox	PCA			3D	2D|3D	2D|3D	2D|3D	2D|3D	2D|3D	2D|3D	2D|3D
colorByGene	(Optional) Enter a gene to color the plot by	enter	PCA				---	---	---	---	---	---	---
pcaGeneSets	(Optional) Store top 200 component genes as	enter	PCA				---	---	---	---	---	---	---
maskGroups	(Optional) Select a groups file to produce separate plots for each group	file	PCA	"note: groups file has three columns (sample ID, group #, group name)"			---	---	---	---	---	---	---
coordinateFile	(Optional) Select a cell/samples 2D coordinate file	file	PCA	"note: this is the ""coordinate"" or ""scores"" file produced by AltAnalyze"			---	---	---	---	---	---	---
classificationAnalysis	Analysis to perform	comboBox	LineageProfiler			cellHarmony	cellHarmony|LineageProfiler	cellHarmony|LineageProfiler	cellHarmony|LineageProfiler	cellHarmony|LineageProfiler	cellHarmony|LineageProfiler	cellHarmony|LineageProfiler	cellHarmony|LineageProfiler
markerFinder_file	Select Reference File	file	LineageProfiler	ICGS or MarkerFinder formatted clustered expression txt file			---	---	---	---	---	---	---
referenceFull	(optional) Full Reference Expression File	file	LineageProfiler	"Un-filtered expression file (txt, mtx or h5)"			---	---	---	---	---	---	---
labels	(optional) Reference Cell Annotation File	file	LineageProfiler	Cell-to-groups annotation/labels txt file for the reference			---	---	---	---	---	---	---
input_lineage_file	Select Query File	file	LineageProfiler	"Un-filtered expression file (txt, mtx or h5)"			---	---	---	---	---	---	---
performDiffExp	Perform differential expression analysis	comboBox	LineageProfiler			yes	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
returnCentroids	Align to cluster centroid instead of cell	comboBox	LineageProfiler			community	community|centroid|cell|None	community|centroid|cell|None	community|centroid|cell|None	community|centroid|cell|None	community|centroid|cell|None	community|centroid|cell|None	community|centroid|cell|None
PearsonThreshold	Enter cellHarmony Pearson correlation threshold	enter	LineageProfiler			0.4	---	---	---	---	---	---	---
FoldCutoff	Enter differential expression fold threshold	enter	LineageProfiler			1.5	---	---	---	---	---	---	---
pvalThreshold	Enter differential expression p-value threshold	enter	LineageProfiler			0.05	---	---	---	---	---	---	---
UseAdjPval	Filter by adjusted rather than raw p-value	comboBox	LineageProfiler			yes	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
geneModel_file	(LineageProflier) Optional restricted marker models	file	LineageProfiler	Note: These options are not applicable for cellHarmony			---	---	---	---	---	---	---
compendiumType	(LineageProflier) Data type to evaluate	comboBox	LineageProfiler			protein_coding	protein_coding|ncRNA|AltExon	protein_coding|ncRNA|AltExon	protein_coding|ncRNA|AltExon	protein_coding|ncRNA|AltExon	protein_coding|ncRNA|AltExon	protein_coding|ncRNA|AltExon	protein_coding|ncRNA|AltExon
compendiumPlatform	(LineageProflier) Reference platform	comboBox	LineageProfiler			exon	exon|gene|3'array	exon|gene|3'array	exon|gene|3'array	exon|gene|3'array	exon|gene|3'array	exon|gene|3'array	exon|gene|3'array
modelDiscovery	(LineageProflier) Iterative model discovery	comboBox	LineageProfiler			no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
input_data_file	Select the tab-delimited expression file for ID Translation	file	IDConverter				---	---	---	---	---	---	---
input_source	Select the file ID system (first column)	comboBox	IDConverter				---	---	---	---	---	---	---
output_source	Select the ID system to add to this file	comboBox	IDConverter				---	---	---	---	---	---	---
input_file1	Select the 1st file to merge	file	MergeFiles				---	---	---	---	---	---	---
input_file2	Select the 2nd file to merge	file	MergeFiles				---	---	---	---	---	---	---
input_file3	Select the 3rd file to merge (optional)	file	MergeFiles				---	---	---	---	---	---	---
input_file4	Select the 4th file to merge (optional)	file	MergeFiles				---	---	---	---	---	---	---
input_file5	Select the 5th file to merge (optional)	file	MergeFiles				---	---	---	---	---	---	---
input_file6	Select the 6th file to merge (optional)	file	MergeFiles				---	---	---	---	---	---	---
input_file7	Select the 7th file to merge (optional)	file	MergeFiles				---	---	---	---	---	---	---
input_file8	Select the 8th file to merge (optional)	file	MergeFiles				---	---	---	---	---	---	---
input_file9	Select the 9th file to merge (optional)	file	MergeFiles				---	---	---	---	---	---	---
input_file10	Select the 10th file to merge (optional)	file	MergeFiles				---	---	---	---	---	---	---
join_option	Join files based on their	comboBox	MergeFiles				Intersection|Union	Intersection|Union	Intersection|Union	Intersection|Union	Intersection|Union	Intersection|Union	Intersection|Union
ID_option	Only return one-to-one ID relationships	comboBox	MergeFiles				False|True	False|True	False|True	False|True	False|True	False|True	False|True
output_merge_dir	Select the folder to save the merged file	folder	MergeFiles				---	---	---	---	---	---	---
venn_input_file1	Select the first file to examine	file	VennDiagram				---	---	---	---	---	---	---
venn_input_file2	Select the second file to examine	file	VennDiagram				---	---	---	---	---	---	---
venn_input_file3	Select the third file to examine (optional)	file	VennDiagram				---	---	---	---	---	---	---
venn_input_file4	Select the fourth file to examine (optional)	file	VennDiagram				---	---	---	---	---	---	---
venn_output_dir	Select the folder to save the examined file	folder	VennDiagram				---	---	---	---	---	---	---
altanalyze_results_folder	Select the AltAnalyze AltResults folder	folder	AltExonViewer				---	---	---	---	---	---	---
data_type	Type of exon-level data to visualize	comboBox	AltExonViewer			raw expression	raw expression|splicing-index	raw expression|splicing-index	raw expression|splicing-index	raw expression|splicing-index	raw expression|splicing-index	raw expression|splicing-index	raw expression|splicing-index
show_introns	Show introns in addition to exons	comboBox	AltExonViewer			no	no|yes	no|yes	no|yes	no|yes	no|yes	no|yes	no|yes
gene_symbol	Enter one or more gene symbols (space delimited)	enter	AltExonViewer				---	---	---	---	---	---	---
analysisType	View as:	comboBox	AltExonViewer			graph-plot	graph-plot|heatmap|Sashimi-Plot	graph-plot|heatmap|Sashimi-Plot	graph-plot|heatmap|Sashimi-Plot	graph-plot|heatmap|Sashimi-Plot	graph-plot|heatmap|Sashimi-Plot	graph-plot|heatmap|Sashimi-Plot	graph-plot|heatmap|Sashimi-Plot
altgenes_file	(optional) Select a file with the first column as genes	file	AltExonViewer				---	---	---	---	---	---	---
Genes_network	Option 1: Build network from entered IDs	enter	network				---	---	---	---	---	---	---
input_ID_file	Option 2: Build network from input ID file (or SIF)	file	network				---	---	---	---	---	---	---
GeneSetSelection_network	Option 3: Build network from GeneSet Type	comboBox	network				---	---	---	---	---	---	---
inputType_network	(Option 2) File type	comboBox	network				ID list|GO-Elite input file|SIF	ID list|GO-Elite input file|SIF	ID list|GO-Elite input file|SIF	ID list|GO-Elite input file|SIF	ID list|GO-Elite input file|SIF	ID list|GO-Elite input file|SIF	ID list|GO-Elite input file|SIF
PathwaySelection_network	(Option 3) Select a specific GeneSet	multiple-comboBox	network				---	---	---	---	---	---	---
OntologyID_network	(Option 3) Type a pathway ID or Ontology ID	enter	network				---	---	---	---	---	---	---
interactionDirs	Select one or more interaction databases	multiple-comboBox	network			WikiPathways|KEGG|BioGRID|TFTargets|common-DrugBank	WikiPathways|KEGG|BioGRID|TFTargets|common-microRNATargets|all-microRNATargets|common-DrugBank|all-DrugBank	WikiPathways|KEGG|BioGRID|TFTargets|common-microRNATargets|all-microRNATargets|common-DrugBank|all-DrugBank	WikiPathways|KEGG|BioGRID|TFTargets|common-microRNATargets|all-microRNATargets|common-DrugBank|all-DrugBank	WikiPathways|KEGG|BioGRID|TFTargets|common-microRNATargets|all-microRNATargets|common-DrugBank|all-DrugBank	WikiPathways|KEGG|BioGRID|TFTargets|common-microRNATargets|all-microRNATargets|common-DrugBank|all-DrugBank	WikiPathways|KEGG|BioGRID|TFTargets|common-microRNATargets|all-microRNATargets|common-DrugBank|all-DrugBank	WikiPathways|KEGG|BioGRID|TFTargets|common-microRNATargets|all-microRNATargets|common-DrugBank|all-DrugBank
degrees	Select interactions of the following type	comboBox	network				direct|indirect|all possible|shortest path	direct|indirect|all possible|shortest path	direct|indirect|all possible|shortest path	direct|indirect|all possible|shortest path	direct|indirect|all possible|shortest path	direct|indirect|all possible|shortest path	direct|indirect|all possible|shortest path
update_interactions	Download/update interaction databases	comboBox	network			no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
elite_exp_file	(optional) Select an input expression file	file	network	note: File should be in GO-Elite format (see GO-Elite help)			---	---	---	---	---	---	---
includeExpIDs_network	Prioritize IDs from expression file in network	comboBox	network			yes	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
output_net_folder	Save results to	folder	network				---	---	---	---	---	---	---
ExpressionCutoff	"Gene expression (TPM, RPKM) cutoff"	enter	PredictGroups			1	---	---	---	---	---	---	---
CountsCutoff	Gene read-count cutoff (not always available)	enter	PredictGroups			0	---	---	---	---	---	---	---
FoldDiff	Fold change filter cutoff	enter	PredictGroups			4	---	---	---	---	---	---	---
rho_cutoff	Minimum Pearson correlation cutoff	enter	PredictGroups			0.2	---	---	---	---	---	---	---
SamplesDiffering	Minimum number of cells differing	enter	PredictGroups			4	---	---	---	---	---	---	---
dynamicCorrelation	ICGS will identify an optimal correlation cutoff	comboBox	PredictGroups			yes	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
removeOutliers	Remove low expression outlier cells	comboBox	PredictGroups			yes	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
featuresToEvaluate	Features to evaluate	comboBox	PredictGroups			Genes	Genes|AltExon|Both	Genes|AltExon|Both	Genes|AltExon|Both	Genes	Genes|AltExon|Both	Genes|AltExon|Both	Genes
restrictBy	Restrict genes to protein coding	comboBox	PredictGroups			yes	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no	yes|no
column_metric_predict	Select the column clustering metric	comboBox	PredictGroups		http://docs.scipy.org/doc/scipy/reference/spatial.distance.html	cosine	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule	braycurtis|canberra|chebyshev|cityblock|correlation|cosine|dice|euclidean|hamming|jaccard|kulsinski|mahalanobis|matching|minkowski|rogerstanimoto|russellrao|seuclidean|sokalmichener|sokalsneath|sqeuclidean|yule
column_method_predict	Select the column clustering method	comboBox	PredictGroups		http://docs.scipy.org/doc/scipy/reference/cluster.hierarchy.html	hopach	average|single|complete|weighted|ward|hopach	average|single|complete|weighted|ward|hopach	average|single|complete|weighted|ward|hopach	average|single|complete|weighted|ward|hopach	average|single|complete|weighted|ward|hopach	average|single|complete|weighted|ward|hopach	average|single|complete|weighted|ward|hopach
k	(optional) number of user-defined ICGS clusters (k)	enter	PredictGroups				---	---	---	---	---	---	---
downsample	(optional) Cells to down-sample to (PageRank)	enter	PredictGroups			2500	---	---	---	---	---	---	---
GeneSelectionPredict	(optional) Enter genes to build clusters from (guides)	enter	PredictGroups				---	---	---	---	---	---	---
GeneSetSelectionPredict	(optional) Or select guide GeneSet/Ontology	comboBox	PredictGroups										
PathwaySelectionPredict	(optional) Select guide specific GeneSet(s)	multiple-comboBox	PredictGroups										
JustShowTheseIDsPredict	(optional) Display selected gene IDs in results	enter	PredictGroups				---	---	---	---	---	---	---
excludeCellCycle	(optional) Eliminate cell cycle effects	comboBox	PredictGroups			no	conservative|stringent|no	conservative|stringent|no	conservative|stringent|no	conservative|stringent|no	conservative|stringent|no	conservative|stringent|no	conservative|stringent|no