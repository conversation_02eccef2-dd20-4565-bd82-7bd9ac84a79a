#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AltAnalyze BED文件坐标处理器
完全按照AltAnalyze原始逻辑处理__junction.bed和__intronJunction.bed文件坐标
"""

import os
import sys
import string
from collections import defaultdict

def cleanUpLine(line):
    """清理行数据，移除换行符和回车符"""
    line = string.replace(line,'\n','')
    line = string.replace(line,'\c','')
    line = string.replace(line,'\r','')
    return line

class JunctionData:
    """Junction数据类，存储junction的所有信息"""
    def __init__(self, chr, strand, exon1_stop, exon2_start, junction_id, biotype):
        self._chr = chr
        self._strand = strand
        self._exon1_stop = exon1_stop
        self._exon2_start = exon2_start
        self._junction_id = junction_id
        self._biotype = biotype
        self._exon_annotations = None
        self._unique_id = None
        self._gene_id = None
        self._exon1_start = None
        self._exon2_stop = None
        self._seq_length = None
    
    def Chr(self): return self._chr
    def Strand(self): return self._strand
    def Exon1Stop(self): return self._exon1_stop
    def Exon2Start(self): return self._exon2_start
    def JunctionID(self): return self._junction_id
    def BioType(self): return self._biotype
    def ExonAnnotations(self): return self._exon_annotations
    def UniqueID(self): return self._unique_id
    def GeneID(self): return self._gene_id
    def Exon1Start(self): return self._exon1_start
    def Exon2Stop(self): return self._exon2_stop
    def SeqLength(self): return self._seq_length
    
    def setExonAnnotations(self, annotations): self._exon_annotations = annotations
    def setUniqueID(self, uid): self._unique_id = uid
    def setGeneID(self, gid): self._gene_id = gid
    def setExon1Start(self, start): self._exon1_start = start
    def setExon2Stop(self, stop): self._exon2_stop = stop
    def setSeqLength(self, length): self._seq_length = length

def process_bed_coordinates(chr, exon1_start, exon2_stop, strand, exon1_len, exon2_len):
    """
    处理BED文件坐标，完全按照AltAnalyze逻辑
    
    参数:
        chr: 染色体名称
        exon1_start: 第一个exon起始位置
        exon2_stop: 第二个exon结束位置  
        strand: 链方向 (+/-)
        exon1_len: 第一个exon长度
        exon2_len: 第二个exon长度
    
    返回:
        tuple: (chr, exon1_stop, exon2_start) - AltAnalyze使用的坐标键
    """
    
    # 染色体名称标准化 - 完全按照AltAnalyze逻辑
    if 'chr' not in chr:
        chr = 'chr' + chr
    if chr == 'chrM': 
        chr = 'chrMT'  # MT是Ensembl约定
    if chr == 'M': 
        chr = 'MT'
    
    # 坐标计算 - 完全按照AltAnalyze逻辑
    if strand == '-':  # 负链处理
        if (exon1_len + exon2_len) == 0:  # Kallisto-Splice特殊情况
            exon1_stop = exon1_start
            exon2_start = exon2_stop
        else:  # 标准情况
            exon1_stop = exon1_start + exon1_len
            exon2_start = exon2_stop - exon2_len + 1
        
        # 负链关键步骤：交换exon顺序
        a = (exon1_start, exon1_stop)
        b = (exon2_start, exon2_stop)
        exon1_stop, exon1_start = b  # 将b的值赋给exon1
        exon2_stop, exon2_start = a  # 将a的值赋给exon2
        
    else:  # 正链处理
        if (exon1_len + exon2_len) == 0:  # Kallisto-Splice特殊情况
            exon1_stop = exon1_start
            exon2_start = exon2_stop
        else:  # 标准情况
            exon1_stop = exon1_start + exon1_len
            exon2_start = exon2_stop - exon2_len + 1
    
    return (chr, exon1_stop, exon2_start)

def parse_bed_file(bed_file_path, reads_threshold=4):
    """
    解析单个BED文件，完全按照AltAnalyze逻辑
    
    参数:
        bed_file_path: BED文件路径
        reads_threshold: reads数量阈值（默认4，与AltAnalyze一致）
    
    返回:
        dict: {(chr, exon1_stop, exon2_start): JunctionData}
    """
    
    junction_db = {}
    
    print(f"解析BED文件: {os.path.basename(bed_file_path)}")
    
    try:
        with open(bed_file_path, 'r') as f:
            for line in f:
                data = cleanUpLine(line)
                if data and data[0] != '#':
                    t = string.split(data, '\t')
                    if len(t) > 11:
                        try:
                            # 提取BED文件的关键列
                            chr, exon1_start, exon2_stop, junction_id, reads, strand = t[:6]
                            lengths = t[10]
                            
                            # 解析lengths字段 - 完全按照AltAnalyze逻辑
                            exon1_len, exon2_len = string.split(lengths, ',')[:2]
                            exon1_len = int(exon1_len)
                            exon2_len = int(exon2_len)
                            exon1_start = int(exon1_start)
                            exon2_stop = int(exon2_stop)
                            
                            # 确定biotype
                            biotype = 'junction'
                            
                            # 过滤条件 - 完全按照AltAnalyze逻辑
                            if float(reads) > reads_threshold:
                                # 处理坐标
                                key = process_bed_coordinates(
                                    chr, exon1_start, exon2_stop, strand, 
                                    exon1_len, exon2_len
                                )
                                
                                # 创建JunctionData对象
                                ji = JunctionData(key[0], strand, key[1], key[2], junction_id, biotype)
                                ji.setExon1Start(exon1_start)
                                ji.setExon2Stop(exon2_stop)
                                
                                # 计算序列长度
                                seq_length = abs(float(key[1] - key[2]))
                                ji.setSeqLength(seq_length)
                                
                                junction_db[key] = ji
                                
                        except Exception:
                            continue  # 跳过无效行
                            
    except Exception as e:
        print(f"解析BED文件失败: {e}")
        return {}
    
    print(f"解析了 {len(junction_db)} 个junction")
    return junction_db

def aggregate_reads_by_coordinates(junction_dbs_list):
    """
    聚合多个BED文件中相同坐标的reads，完全按照AltAnalyze逻辑

    参数:
        junction_dbs_list: list of dict，每个dict是parse_bed_file的返回值

    返回:
        tuple: (merged_junction_db, reads_count_db)
            - merged_junction_db: 合并后的junction数据库
            - reads_count_db: {(chr, exon1_stop, exon2_start): total_reads}
    """

    merged_junction_db = {}
    reads_count_db = defaultdict(int)

    print("开始聚合reads...")

    for i, junction_db in enumerate(junction_dbs_list):
        print(f"处理第 {i+1} 个文件的数据...")

        for key, ji in junction_db.items():
            # 如果是第一次遇到这个坐标，直接存储
            if key not in merged_junction_db:
                merged_junction_db[key] = ji
                # 从junction_id中提取reads数量（如果可能）
                try:
                    # 尝试从各种可能的格式中提取reads
                    junction_id = ji.JunctionID()
                    if ':' in junction_id and '-' in junction_id:
                        # 假设reads信息在其他地方，这里设为1作为占位
                        reads_count_db[key] += 1
                except:
                    reads_count_db[key] += 1
            else:
                # 相同坐标的reads相加 - 完全按照AltAnalyze逻辑
                reads_count_db[key] += 1

    print(f"聚合完成，共有 {len(merged_junction_db)} 个unique坐标")
    print(f"总reads计数: {sum(reads_count_db.values())}")

    return merged_junction_db, dict(reads_count_db)

def process_bed_directory(bed_dir, reads_threshold=4, file_patterns=None):
    """
    处理整个BED目录，完全按照AltAnalyze工作流程

    参数:
        bed_dir: BED文件目录路径
        reads_threshold: reads阈值
        file_patterns: 文件模式列表，如['__junction.bed', '__intronJunction.bed']

    返回:
        tuple: (merged_junction_db, reads_count_db, file_stats)
    """

    if file_patterns is None:
        file_patterns = ['__junction.bed', '__intronJunction.bed', '.bed']

    print(f"扫描目录: {bed_dir}")

    # 查找BED文件
    bed_files = []
    for filename in os.listdir(bed_dir):
        if filename.endswith('.bed') or filename.endswith('.BED'):
            # 检查是否匹配指定模式
            for pattern in file_patterns:
                if pattern in filename:
                    bed_files.append(os.path.join(bed_dir, filename))
                    break
            else:
                # 如果没有特定模式，包含所有.bed文件
                if '.bed' in file_patterns:
                    bed_files.append(os.path.join(bed_dir, filename))

    if not bed_files:
        print(f"错误: 在目录 {bed_dir} 中未找到匹配的BED文件")
        return {}, {}, {}

    print(f"找到 {len(bed_files)} 个BED文件:")
    for bed_file in bed_files:
        print(f"  - {os.path.basename(bed_file)}")

    # 解析所有BED文件
    junction_dbs_list = []
    file_stats = {}

    for bed_file in bed_files:
        junction_db = parse_bed_file(bed_file, reads_threshold)
        junction_dbs_list.append(junction_db)
        file_stats[os.path.basename(bed_file)] = len(junction_db)

    # 聚合reads
    merged_junction_db, reads_count_db = aggregate_reads_by_coordinates(junction_dbs_list)

    return merged_junction_db, reads_count_db, file_stats

def generate_coordinate_mapping(merged_junction_db, reads_count_db):
    """
    生成坐标映射表，类似counts.original.txt的格式

    参数:
        merged_junction_db: 合并后的junction数据库
        reads_count_db: reads计数数据库

    返回:
        list: [(coordinate_string, reads_count, junction_info), ...]
    """

    coordinate_mapping = []

    for key in sorted(merged_junction_db.keys()):
        ji = merged_junction_db[key]
        chr_name, exon1_stop, exon2_start = key

        # 生成坐标字符串
        coordinates = f"{chr_name}:{exon1_stop}-{exon2_start}"

        # 获取reads计数
        reads_count = reads_count_db.get(key, 0)

        # 收集junction信息
        junction_info = {
            'junction_id': ji.JunctionID(),
            'strand': ji.Strand(),
            'biotype': ji.BioType(),
            'seq_length': ji.SeqLength(),
            'exon1_start': ji.Exon1Start(),
            'exon2_stop': ji.Exon2Stop()
        }

        coordinate_mapping.append((coordinates, reads_count, junction_info))

    return coordinate_mapping

def write_coordinate_mapping(coordinate_mapping, output_file):
    """
    将坐标映射写入文件

    参数:
        coordinate_mapping: generate_coordinate_mapping的返回值
        output_file: 输出文件路径
    """

    print(f"写入坐标映射到: {output_file}")

    with open(output_file, 'w') as f:
        # 写入表头
        headers = [
            'Coordinates', 'Reads_Count', 'Junction_ID', 'Strand',
            'BioType', 'Seq_Length', 'Exon1_Start', 'Exon2_Stop'
        ]
        f.write('\t'.join(headers) + '\n')

        # 写入数据
        for coordinates, reads_count, junction_info in coordinate_mapping:
            values = [
                coordinates,
                str(reads_count),
                junction_info['junction_id'],
                junction_info['strand'],
                junction_info['biotype'],
                str(junction_info['seq_length']) if junction_info['seq_length'] else 'N/A',
                str(junction_info['exon1_start']) if junction_info['exon1_start'] else 'N/A',
                str(junction_info['exon2_stop']) if junction_info['exon2_stop'] else 'N/A'
            ]
            f.write('\t'.join(values) + '\n')

    print(f"已写入 {len(coordinate_mapping)} 条记录")

def write_counts_format(merged_junction_db, reads_count_db, output_file):
    """
    输出类似counts.original.txt格式的文件

    参数:
        merged_junction_db: 合并后的junction数据库
        reads_count_db: reads计数数据库
        output_file: 输出文件路径
    """

    print(f"写入counts格式到: {output_file}")

    with open(output_file, 'w') as f:
        f.write("AltAnalyze_ID\tReads_Count\n")

        for key in sorted(merged_junction_db.keys()):
            chr_name, exon1_stop, exon2_start = key
            coordinates = f"{chr_name}:{exon1_stop}-{exon2_start}"
            reads_count = reads_count_db.get(key, 0)

            # 生成简单的AltAnalyze风格ID
            altanalyze_id = f"JUNCTION_{coordinates}"

            f.write(f"{altanalyze_id}={coordinates}\t{reads_count}\n")

    print(f"已写入 {len(merged_junction_db)} 条记录")

def main_process_bed_files(bed_dir, output_prefix="bed_coordinate_analysis", reads_threshold=4):
    """
    主处理函数，完整的BED文件坐标处理流程

    参数:
        bed_dir: BED文件目录
        output_prefix: 输出文件前缀
        reads_threshold: reads阈值

    返回:
        dict: 处理结果统计
    """

    print("=" * 60)
    print("AltAnalyze BED文件坐标处理器")
    print("=" * 60)
    print(f"输入目录: {bed_dir}")
    print(f"Reads阈值: {reads_threshold}")
    print(f"输出前缀: {output_prefix}")
    print("=" * 60)

    # 处理BED目录
    merged_junction_db, reads_count_db, file_stats = process_bed_directory(
        bed_dir, reads_threshold
    )

    if not merged_junction_db:
        print("错误: 未找到有效的junction数据")
        return {}

    # 生成坐标映射
    coordinate_mapping = generate_coordinate_mapping(merged_junction_db, reads_count_db)

    # 输出结果
    mapping_file = f"{output_prefix}_coordinate_mapping.txt"
    counts_file = f"{output_prefix}_counts_format.txt"

    write_coordinate_mapping(coordinate_mapping, mapping_file)
    write_counts_format(merged_junction_db, reads_count_db, counts_file)

    # 统计信息
    stats = {
        'total_files_processed': len(file_stats),
        'total_unique_coordinates': len(merged_junction_db),
        'total_reads': sum(reads_count_db.values()),
        'file_stats': file_stats,
        'output_files': [mapping_file, counts_file]
    }

    print("\n" + "=" * 60)
    print("处理完成!")
    print(f"处理文件数: {stats['total_files_processed']}")
    print(f"唯一坐标数: {stats['total_unique_coordinates']}")
    print(f"总reads数: {stats['total_reads']}")
    print("文件统计:")
    for filename, count in file_stats.items():
        print(f"  {filename}: {count} junctions")
    print(f"输出文件: {', '.join(stats['output_files'])}")
    print("=" * 60)

    return stats

# 示例使用
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(
        description='AltAnalyze BED文件坐标处理器',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 基本用法
  python altanalyze_bed_coordinate_processor.py --bed_dir /mnt/bed

  # 指定输出前缀和reads阈值
  python altanalyze_bed_coordinate_processor.py --bed_dir /mnt/bed --output analysis_result --threshold 10
        """
    )

    parser.add_argument('--bed_dir', required=True,
                       help='包含BED文件的目录路径')
    parser.add_argument('--output', default='bed_coordinate_analysis',
                       help='输出文件前缀 (默认: bed_coordinate_analysis)')
    parser.add_argument('--threshold', type=int, default=4,
                       help='reads数量阈值 (默认: 4)')

    args = parser.parse_args()

    # 验证输入
    if not os.path.exists(args.bed_dir):
        print(f"错误: 目录 {args.bed_dir} 不存在")
        sys.exit(1)

    # 运行主处理函数
    stats = main_process_bed_files(args.bed_dir, args.output, args.threshold)
