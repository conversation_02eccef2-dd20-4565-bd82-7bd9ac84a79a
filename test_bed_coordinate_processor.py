#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试AltAnalyze BED文件坐标处理器
"""

import os
import tempfile
import shutil
from altanalyze_bed_coordinate_processor import (
    process_bed_coordinates, parse_bed_file, main_process_bed_files
)

def create_test_bed_files():
    """创建测试用的BED文件"""
    
    # 创建临时目录
    test_dir = tempfile.mkdtemp(prefix='test_bed_')
    
    # 创建测试的junction.bed文件
    junction_bed_content = """chrX	100630759	100629986	JUNC1:100630759-100629986	6	+	100630759	100629986	255,0,0	2	773,1	0,772
chr17	37566505	37566506	JUNC2:37566505-37566506	6	-	37566505	37566506	255,0,0	2	1,1	0,0
chr1	1000000	2000000	JUNC3:1000000-2000000	150	+	1000000	2000000	255,0,0	2	50,50	0,999950
chr2	3000000	4000000	JUNC4:3000000-4000000	3	+	3000000	4000000	255,0,0	2	50,50	0,999950"""

    # 创建测试的intronJunction.bed文件
    intron_bed_content = """chrX	100630759	100629986	JUNC1:100630759-100629986	6	+	100630759	100629986	255,0,0	2	773,1	0,772
chr17	37566505	37566506	JUNC2:37566505-37566506	6	-	37566505	37566506	255,0,0	2	1,1	0,0
chr3	5000000	6000000	JUNC5:5000000-6000000	25	-	5000000	6000000	255,0,0	2	100,100	0,999900"""

    junction_file = os.path.join(test_dir, 'sample1__junction.bed')
    intron_file = os.path.join(test_dir, 'sample1__intronJunction.bed')
    
    with open(junction_file, 'w') as f:
        f.write(junction_bed_content)
    
    with open(intron_file, 'w') as f:
        f.write(intron_bed_content)
    
    return test_dir, junction_file, intron_file

def test_coordinate_processing():
    """测试坐标处理函数"""
    
    print("测试坐标处理函数...")
    
    # 测试正链
    result = process_bed_coordinates('X', 100630759, 100629986, '+', 773, 1)
    expected = ('chrX', 100631532, 100629986)  # 100630759+773, 100629986-1+1
    print(f"正链测试: {result} (期望: {expected})")
    
    # 测试负链
    result = process_bed_coordinates('17', 37566505, 37566506, '-', 1, 1)
    print(f"负链测试: {result}")
    
    # 测试Kallisto-Splice特殊情况
    result = process_bed_coordinates('1', 1000000, 2000000, '+', 0, 0)
    expected = ('chr1', 1000000, 2000000)
    print(f"Kallisto-Splice测试: {result} (期望: {expected})")
    
    print("坐标处理测试完成\n")

def test_bed_file_parsing():
    """测试BED文件解析"""
    
    print("测试BED文件解析...")
    
    test_dir, junction_file, intron_file = create_test_bed_files()
    
    try:
        # 解析junction文件
        junction_db = parse_bed_file(junction_file, reads_threshold=4)
        print(f"Junction文件解析结果: {len(junction_db)} 个junction")
        
        # 解析intron文件
        intron_db = parse_bed_file(intron_file, reads_threshold=4)
        print(f"Intron文件解析结果: {len(intron_db)} 个junction")
        
        # 显示解析的坐标
        print("Junction文件坐标:")
        for key, ji in junction_db.items():
            print(f"  {key} -> {ji.JunctionID()}")
        
        print("Intron文件坐标:")
        for key, ji in intron_db.items():
            print(f"  {key} -> {ji.JunctionID()}")
        
    finally:
        # 清理临时文件
        shutil.rmtree(test_dir)
    
    print("BED文件解析测试完成\n")

def test_full_workflow():
    """测试完整工作流程"""
    
    print("测试完整工作流程...")
    
    test_dir, junction_file, intron_file = create_test_bed_files()
    
    try:
        # 运行完整处理流程
        stats = main_process_bed_files(test_dir, "test_output", reads_threshold=4)
        
        print("处理统计:")
        for key, value in stats.items():
            if key != 'file_stats':
                print(f"  {key}: {value}")
        
        # 检查输出文件
        for output_file in stats['output_files']:
            if os.path.exists(output_file):
                print(f"输出文件 {output_file} 创建成功")
                # 显示前几行内容
                with open(output_file, 'r') as f:
                    lines = f.readlines()[:5]
                    print(f"  前5行内容:")
                    for line in lines:
                        print(f"    {line.strip()}")
            else:
                print(f"输出文件 {output_file} 未找到")
        
    finally:
        # 清理临时文件
        shutil.rmtree(test_dir)
        # 清理输出文件
        for output_file in ['test_output_coordinate_mapping.txt', 'test_output_counts_format.txt']:
            if os.path.exists(output_file):
                os.remove(output_file)
    
    print("完整工作流程测试完成\n")

def test_coordinate_aggregation():
    """测试坐标聚合逻辑"""
    
    print("测试坐标聚合逻辑...")
    
    # 模拟相同坐标在不同文件中的情况
    test_dir, junction_file, intron_file = create_test_bed_files()
    
    try:
        junction_db = parse_bed_file(junction_file, reads_threshold=0)  # 降低阈值以包含所有数据
        intron_db = parse_bed_file(intron_file, reads_threshold=0)
        
        # 查找相同坐标
        common_keys = set(junction_db.keys()) & set(intron_db.keys())
        print(f"相同坐标数量: {len(common_keys)}")
        
        for key in common_keys:
            print(f"相同坐标: {key}")
            print(f"  Junction文件: {junction_db[key].JunctionID()}")
            print(f"  Intron文件: {intron_db[key].JunctionID()}")
        
    finally:
        shutil.rmtree(test_dir)
    
    print("坐标聚合测试完成\n")

if __name__ == "__main__":
    print("=" * 60)
    print("AltAnalyze BED坐标处理器测试")
    print("=" * 60)
    
    test_coordinate_processing()
    test_bed_file_parsing()
    test_coordinate_aggregation()
    test_full_workflow()
    
    print("=" * 60)
    print("所有测试完成!")
    print("=" * 60)
