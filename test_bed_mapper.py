#!/usr/bin/env python
"""
测试bed_to_altanalyze_id_mapper.py脚本的功能
创建示例数据并验证映射结果
"""

import os
import tempfile
import shutil
from bed_to_altanalyze_id_mapper import *

def create_test_bed_file(bed_file_path, sample_name):
    """创建测试用的bed文件"""
    
    # 示例bed数据 - 模拟真实的junction.bed格式
    bed_data = [
        # chr, start, end, junction_id, reads, strand, ..., lengths
        # Known junction示例
        "chrX\t100630759\t100629986\tJUNC1:100630759-100629986\t630\t+\t100630759\t100629986\t255,0,0\t2\t773,1\t0,772",
        
        # Intron retention示例（坐标差为1）
        "chr17\t37566505\t37566506\tJUNC2:37566505-37566506\t12\t-\t37566505\t37566506\t255,0,0\t2\t1,1\t0,0",
        
        # Novel junction示例
        "chr1\t5000000\t6000000\tJUNC3:5000000-6000000\t150\t+\t5000000\t6000000\t255,0,0\t2\t50,50\t0,999950",
        
        # 低reads数的junction（应该被过滤）
        "chr2\t7000000\t8000000\tJUNC4:7000000-8000000\t3\t+\t7000000\t8000000\t255,0,0\t2\t50,50\t0,999950",
        
        # 另一个known junction
        "chr1\t1000000\t2000000\tJUNC5:1000000-2000000\t85\t-\t1000000\t2000000\t255,0,0\t2\t100,100\t0,999900",
    ]
    
    with open(bed_file_path, 'w') as f:
        for line in bed_data:
            f.write(line + '\n')

def create_test_intron_bed_file(bed_file_path, sample_name):
    """创建测试用的intronJunction.bed文件"""
    
    bed_data = [
        # 更多intron retention示例
        "chr3\t9000000\t9000001\tINTRON1:9000000-9000001\t8\t+\t9000000\t9000001\t255,0,0\t2\t1,1\t0,0",
        "chr4\t11000000\t11000001\tINTRON2:11000000-11000001\t15\t-\t11000000\t11000001\t255,0,0\t2\t1,1\t0,0",
    ]
    
    with open(bed_file_path, 'w') as f:
        for line in bed_data:
            f.write(line + '\n')

def create_test_annotation_file(annotation_file_path):
    """创建测试用的注释文件"""
    
    annotations = [
        "chrX:100630759-100629986\tENSG00000000003\tE11.1-E13.1",
        "chr17:37566506-37566505\tENSG00000000003\tI25.1", 
        "chr1:1000000-2000000\tENSG00000000001\tE1.1-E2.1",
        "chr3:9000000-9000001\tENSG00000000004\tI15.1",
        "chr4:11000000-11000001\tENSG00000000005\tI8.1",
    ]
    
    with open(annotation_file_path, 'w') as f:
        f.write("# Test annotation file\n")
        for line in annotations:
            f.write(line + '\n')

def run_test():
    """运行完整的测试"""
    
    print("=" * 60)
    print("开始测试bed_to_altanalyze_id_mapper.py")
    print("=" * 60)
    
    # 创建临时目录
    test_dir = tempfile.mkdtemp(prefix='bed_mapper_test_')
    print(f"测试目录: {test_dir}")
    
    try:
        # 创建测试文件
        bed_dir = os.path.join(test_dir, 'bed_files')
        os.makedirs(bed_dir)
        
        # 创建bed文件
        junction_bed = os.path.join(bed_dir, 'sample1__junction.bed')
        intron_bed = os.path.join(bed_dir, 'sample1__intronJunction.bed')
        annotation_file = os.path.join(test_dir, 'test_annotations.txt')
        output_file = os.path.join(test_dir, 'test_mapping.txt')
        
        create_test_bed_file(junction_bed, 'sample1')
        create_test_intron_bed_file(intron_bed, 'sample1')
        create_test_annotation_file(annotation_file)
        
        print(f"创建了测试文件:")
        print(f"  - {junction_bed}")
        print(f"  - {intron_bed}")
        print(f"  - {annotation_file}")
        
        # 运行映射
        print("\n开始处理...")
        junction_db, junction_simple_db = process_bed_directory(
            bed_dir, 'Hs', annotation_file, None
        )
        
        # 输出结果
        write_mapping_results(junction_db, junction_simple_db, output_file)
        write_counts_format(junction_simple_db, output_file)
        
        # 验证结果
        print("\n验证结果:")
        
        # 检查输出文件
        if os.path.exists(output_file):
            print(f"✓ 映射文件已生成: {output_file}")
            
            # 读取并显示结果
            with open(output_file, 'r') as f:
                lines = f.readlines()
                print(f"✓ 输出文件包含 {len(lines)} 行")
                
                # 显示前几行
                print("\n输出文件内容预览:")
                for i, line in enumerate(lines[:6]):  # 显示表头+5行数据
                    print(f"  {i+1}: {line.strip()}")
        
        # 检查counts格式文件
        counts_file = output_file.replace('.txt', '_counts_format.txt')
        if os.path.exists(counts_file):
            print(f"✓ Counts格式文件已生成: {counts_file}")
            
            with open(counts_file, 'r') as f:
                lines = f.readlines()
                print(f"✓ Counts文件包含 {len(lines)} 行")
                
                print("\nCounts文件内容预览:")
                for i, line in enumerate(lines[:6]):
                    print(f"  {i+1}: {line.strip()}")
        
        # 验证关键功能
        print("\n功能验证:")
        
        # 检查是否正确识别了known和novel junction
        known_count = sum(1 for ji in junction_db.values() if ji.ExonAnnotations() is not None)
        novel_count = len(junction_db) - known_count
        print(f"✓ Known junction: {known_count}")
        print(f"✓ Novel junction: {novel_count}")
        
        # 检查过滤功能
        total_junctions = len(junction_db)
        passed_junctions = len(junction_simple_db)
        filtered_junctions = total_junctions - passed_junctions
        print(f"✓ 总junction数: {total_junctions}")
        print(f"✓ 通过过滤: {passed_junctions}")
        print(f"✓ 被过滤: {filtered_junctions}")
        
        # 检查ID格式
        print(f"\n生成的AltAnalyze ID示例:")
        for i, (key, uid) in enumerate(junction_simple_db.items()):
            if i < 3:  # 显示前3个
                chr_name, exon1_stop, exon2_start = key
                coords = f"{chr_name}:{exon1_stop}-{exon2_start}"
                print(f"  {coords} -> {uid}")
        
        print(f"\n✓ 测试完成！所有文件保存在: {test_dir}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 询问是否删除测试目录
        response = input(f"\n是否删除测试目录 {test_dir}? (y/N): ")
        if response.lower() == 'y':
            shutil.rmtree(test_dir)
            print("测试目录已删除")
        else:
            print(f"测试目录保留: {test_dir}")

def main():
    """主函数"""
    success = run_test()
    
    if success:
        print("\n" + "=" * 60)
        print("测试成功完成！")
        print("脚本功能验证通过，可以用于实际数据处理。")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("测试失败！")
        print("请检查错误信息并修复问题。")
        print("=" * 60)

if __name__ == "__main__":
    main()
