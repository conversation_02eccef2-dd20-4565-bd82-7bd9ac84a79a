# AltAnalyze # 

An automated cross-platform workflow for RNA-Seq gene, splicing and pathway analysis 

AltAnalyze is an [extremely user-friendly](https://www.youtube.com/results?search_query=altanalyze) and open-source analysis tool that can be used for a broad range of genomics analyses. These analyses include the direct processing of raw single-cell and bulk [RNASeq](https://github.com/nsalomonis/altanalyze/wiki/RNASeq) or microarray data files, advanced methods for [single-cell population discovery](https://www.ncbi.nlm.nih.gov/pubmed/32207533) and [dataset comparison](https://www.ncbi.nlm.nih.gov/pubmed/31529053), differential expression analyses, analysis of alternative splicing/promoter/polyadenylation and advanced isoform function prediction analysis (protein, domain and microRNA targeting). Multiple advanced visualization tools and [à la carte analysis methods](https://github.com/nsalomonis/altanalyze/wiki/Tutorials) are supported in AltAnalyze (e.g., network, pathway, splicing graph). AltAnalyze is compatible with various data inputs for [RNASeq](https://github.com/nsalomonis/altanalyze/wiki/RNASeq) data ([FASTQ](http://altanalyze.blogspot.com/2016/08/using-ultrafast-sequence.html), [BAM](http://altanalyze.blogspot.com/2016/08/bye-bye-bed-files-welcome-bam.html), BED), microarray platforms ([Gene 1.0](https://github.com/nsalomonis/altanalyze/wiki/AffyGeneArray), [Exon 1.0](https://github.com/nsalomonis/altanalyze/wiki/AffyExonArray), [junction](https://github.com/nsalomonis/altanalyze/wiki/JAY) and [3' arrays](https://github.com/nsalomonis/altanalyze/wiki/CompatibleArrays)) for automated gene expression and splicing analysis. This software requires no advanced knowledge of bioinformatics programs or scripting or advanced computer hardware. User friendly [videos](https://www.google.com/#q=altanalyze&tbm=vid), [online tutorials](https://github.com/nsalomonis/altanalyze/wiki/Tutorials) and [blog posts](http://altanalyze.blogspot.com/) are also available.

# Dependencies # 

If installed from PyPI (pip install AltAnalyze), the below dependencies should be included in the installed package. When running from source code you will need to install the following libraries.
  * Required: Python 2.7, numpy, scipy, matplotlib, sklearn (scikit-learn)
  * Recommended: umap-learn, nimfa, numba, python-louvain, annoy, networkx, R 3+, fastcluster, pillow, pysam, requests, python-igraph, cairo

[AltAnalyze documentation](http://altanalyze.readthedocs.io/), stand-alone archives are provided at [sourceforge](https://sourceforge.net/projects/altanalyze/files/) as well as at [github](https://github.com/nsalomonis/altanalyze). For questions not addressed here, please [contact us](https://github.com/nsalomonis/altanalyze/wiki/ContactUs).

**News Update** [5/17/20](https://github.com/nsalomonis/altanalyze/wiki/News)

![http://altanalyze.readthedocs.io/en/latest/images/AltAnalyzeOverview.gif](http://altanalyze.readthedocs.io/en/latest/images/AltAnalyzeOverview.gif)
