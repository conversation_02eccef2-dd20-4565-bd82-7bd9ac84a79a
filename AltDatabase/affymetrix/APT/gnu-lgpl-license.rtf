{\rtf1\adeflang1025\ansi\ansicpg1252\uc1\adeff0\deff0\stshfdbch14\stshfloch0\stshfhich0\stshfbi0\deflang1033\deflangfe1028{\fonttbl{\f0\froman\fcharset0\fprq2{\*\panose 02020603050405020304}Times New Roman;}{\f2\fmodern\fcharset0\fprq1{\*\panose 02070309020205020404}Courier New;}
{\f14\fnil\fcharset136\fprq2{\*\panose 02010601000101010101}PMingLiU{\*\falt Arial Unicode MS};}{\f153\fnil\fcharset136\fprq2{\*\panose 00000000000000000000}@PMingLiU;}{\f154\froman\fcharset238\fprq2 Times New Roman CE;}
{\f155\froman\fcharset204\fprq2 Times New Roman Cyr;}{\f157\froman\fcharset161\fprq2 Times New Roman Greek;}{\f158\froman\fcharset162\fprq2 Times New Roman Tur;}{\f159\fbidi \froman\fcharset177\fprq2 Times New Roman (Hebrew);}
{\f160\fbidi \froman\fcharset178\fprq2 Times New Roman (Arabic);}{\f161\froman\fcharset186\fprq2 Times New Roman Baltic;}{\f162\froman\fcharset163\fprq2 Times New Roman (Vietnamese);}{\f174\fmodern\fcharset238\fprq1 Courier New CE;}
{\f175\fmodern\fcharset204\fprq1 Courier New Cyr;}{\f177\fmodern\fcharset161\fprq1 Courier New Greek;}{\f178\fmodern\fcharset162\fprq1 Courier New Tur;}{\f179\fbidi \fmodern\fcharset177\fprq1 Courier New (Hebrew);}
{\f180\fbidi \fmodern\fcharset178\fprq1 Courier New (Arabic);}{\f181\fmodern\fcharset186\fprq1 Courier New Baltic;}{\f182\fmodern\fcharset163\fprq1 Courier New (Vietnamese);}}{\colortbl;\red0\green0\blue0;\red0\green0\blue255;\red0\green255\blue255;
\red0\green255\blue0;\red255\green0\blue255;\red255\green0\blue0;\red255\green255\blue0;\red255\green255\blue255;\red0\green0\blue128;\red0\green128\blue128;\red0\green128\blue0;\red128\green0\blue128;\red128\green0\blue0;\red128\green128\blue0;
\red128\green128\blue128;\red192\green192\blue192;}{\stylesheet{\ql \li0\ri0\widctlpar\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0 \rtlch\fcs1 \af0\afs24\alang1025 \ltrch\fcs0 
\fs24\lang1033\langfe1028\loch\f0\hich\af0\dbch\af14\cgrid\langnp1033\langfenp1028 \snext0 Normal;}{\*\cs10 \additive \ssemihidden Default Paragraph Font;}{\*
\ts11\tsrowd\trftsWidthB3\trpaddl108\trpaddr108\trpaddfl3\trpaddft3\trpaddfb3\trpaddfr3\trcbpat1\trcfpat1\tscellwidthfts0\tsvertalt\tsbrdrt\tsbrdrl\tsbrdrb\tsbrdrr\tsbrdrdgl\tsbrdrdgr\tsbrdrh\tsbrdrv 
\ql \li0\ri0\widctlpar\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0 \rtlch\fcs1 \af0\afs20 \ltrch\fcs0 \fs20\lang1024\langfe1024\loch\f0\hich\af0\dbch\af14\cgrid\langnp1024\langfenp1024 \snext11 \ssemihidden Normal Table;}{
\s15\ql \li0\ri0\widctlpar\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0 \rtlch\fcs1 \af2\afs20\alang1025 \ltrch\fcs0 \fs20\lang1033\langfe1028\loch\f2\hich\af2\dbch\af14\cgrid\langnp1033\langfenp1028 \sbasedon0 \snext15 \styrsid11168331 Plain Text;}
}{\*\latentstyles\lsdstimax156\lsdlockeddef0}{\*\rsidtbl \rsid474\rsid68730\rsid77744\rsid200972\rsid202343\rsid340389\rsid413488\rsid467166\rsid656002\rsid726551\rsid729287\rsid936952\rsid1004773\rsid1014356\rsid1265243\rsid1332399\rsid1589327
\rsid1596877\rsid1709434\rsid1781109\rsid1985473\rsid1985832\rsid1997227\rsid2036987\rsid2060446\rsid2164318\rsid2243883\rsid2253916\rsid2447962\rsid2567185\rsid2567637\rsid2622546\rsid2777930\rsid2838836\rsid2964589\rsid3353643\rsid3565487\rsid3804266
\rsid3825468\rsid4004439\rsid4130868\rsid4141992\rsid4338816\rsid4406752\rsid4603602\rsid4721804\rsid4813120\rsid4855380\rsid5267079\rsid5311350\rsid5323653\rsid5506723\rsid5509841\rsid5655055\rsid5716959\rsid5777070\rsid5784793\rsid5787125\rsid5794163
\rsid5845810\rsid5901623\rsid6231793\rsid6256615\rsid6301864\rsid6366727\rsid6824997\rsid6969006\rsid7098056\rsid7100641\rsid7175781\rsid7292684\rsid7484807\rsid7634859\rsid7872531\rsid7934035\rsid7942967\rsid7995404\rsid8078353\rsid8401500\rsid8484561
\rsid8608306\rsid8655586\rsid8721664\rsid8735443\rsid8852293\rsid9047685\rsid9327835\rsid9336856\rsid9440601\rsid9516821\rsid9586365\rsid9642140\rsid9769593\rsid9906650\rsid9968313\rsid9992571\rsid10184217\rsid10230039\rsid10359293\rsid10365952
\rsid10581881\rsid10639865\rsid10834591\rsid10899037\rsid11164892\rsid11168331\rsid11303133\rsid11403804\rsid11431521\rsid11545965\rsid11558645\rsid11560683\rsid11562399\rsid11564247\rsid11621421\rsid11676765\rsid11684437\rsid11750258\rsid11807751
\rsid12060664\rsid12079163\rsid12273124\rsid12340957\rsid12353219\rsid12593538\rsid12675781\rsid12733557\rsid12781090\rsid12870658\rsid13047990\rsid13071164\rsid13139276\rsid13201580\rsid13331796\rsid13388756\rsid13442406\rsid13839155\rsid13960660
\rsid14112554\rsid14164144\rsid14184705\rsid14366939\rsid14372153\rsid14497332\rsid14499682\rsid14578437\rsid14629096\rsid14631914\rsid14634800\rsid14680120\rsid14682807\rsid14751681\rsid14820212\rsid14899707\rsid14902208\rsid15081954\rsid15084235
\rsid15354878\rsid15408241\rsid15478159\rsid15564113\rsid15597980\rsid15731396\rsid15737555\rsid16004358\rsid16009437\rsid16187444\rsid16334942\rsid16522230\rsid16536646\rsid16545256\rsid16668395\rsid16737298}{\*\generator Microsoft Word 11.0.6359;}{\info
{\author Alan Williams}{\operator Alan Williams}{\creatim\yr2006\mo4\dy10\hr21\min46}{\revtim\yr2006\mo4\dy10\hr21\min46}{\version2}{\edmins1}{\nofpages12}{\nofwords4020}{\nofchars22915}{\*\company Affymetrix, Inc.}{\nofcharsws26882}{\vern24703}}
\margl1319\margr1319\ltrsect \widowctrl\ftnbj\aenddoc\noxlattoyen\expshrtn\noultrlspc\dntblnsbdb\nospaceforul\formshade\horzdoc\dgmargin\dghspace180\dgvspace180\dghorigin1319\dgvorigin1440\dghshow1\dgvshow1
\jexpand\viewkind1\viewscale100\pgbrdrhead\pgbrdrfoot\splytwnine\ftnlytwnine\htmautsp\nolnhtadjtbl\useltbaln\alntblind\lytcalctblwd\lyttblrtgr\lnbrkrule\nobrkwrptbl\snaptogridincell\allowfieldendsel\wrppunct\asianbrkrule\nojkernpunct\rsidroot2060446 \fet0
\ltrpar \sectd \ltrsect\linex0\endnhere\sectlinegrid360\sectdefaultcl\sectrsid11168331\sftnbj {\*\pnseclvl1\pnucrm\pnqc\pnstart1\pnindent720\pnhang {\pntxta .}}{\*\pnseclvl2\pnucltr\pnqc\pnstart1\pnindent720\pnhang {\pntxta .}}{\*\pnseclvl3
\pndec\pnqc\pnstart1\pnindent720\pnhang {\pntxta .}}{\*\pnseclvl4\pnlcltr\pnqc\pnstart1\pnindent720\pnhang {\pntxta )}}{\*\pnseclvl5\pndec\pnqc\pnstart1\pnindent720\pnhang {\pntxtb (}{\pntxta )}}{\*\pnseclvl6\pnlcltr\pnqc\pnstart1\pnindent720\pnhang 
{\pntxtb (}{\pntxta )}}{\*\pnseclvl7\pnlcrm\pnqc\pnstart1\pnindent720\pnhang {\pntxtb (}{\pntxta )}}{\*\pnseclvl8\pnlcltr\pnqc\pnstart1\pnindent720\pnhang {\pntxtb (}{\pntxta )}}{\*\pnseclvl9\pnlcrm\pnqc\pnstart1\pnindent720\pnhang {\pntxtb (}{\pntxta )}}
\pard\plain \ltrpar\s15\ql \li0\ri0\widctlpar\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid11168331 \rtlch\fcs1 \af2\afs20\alang1025 \ltrch\fcs0 \fs20\lang1033\langfe1028\loch\af2\hich\af2\dbch\af14\cgrid\langnp1033\langfenp1028 {
\rtlch\fcs1 \af2 \ltrch\fcs0 \insrsid2447962\charrsid11168331 \tab \tab \hich\af2\dbch\af14\loch\f2   GNU LESSER GENERAL PUBLIC LICENSE
\par \tab \tab \hich\af2\dbch\af14\loch\f2        Version 2.1, February 1999
\par 
\par \hich\af2\dbch\af14\loch\f2  Copyright (C) 1991, 1999 Free Software Foundation, Inc.
\par \hich\af2\dbch\af14\loch\f2      51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
\par \hich\af2\dbch\af14\loch\f2  Everyone is permitted to copy and distribute verbatim co\hich\af2\dbch\af14\loch\f2 pies
\par \hich\af2\dbch\af14\loch\f2  of this license document, but changing it is not allowed.
\par 
\par \hich\af2\dbch\af14\loch\f2 [This is the first released version of the Lesser GPL.  It also counts
\par \hich\af2\dbch\af14\loch\f2  as the successor of the GNU Library Public License, version 2, hence
\par \hich\af2\dbch\af14\loch\f2  the version number 2.1.]
\par 
\par \tab \tab \tab \hich\af2\dbch\af14\loch\f2     Preamble
\par 
\par \hich\af2\dbch\af14\loch\f2   The licenses for most software are designed to take away your
\par \hich\af2\dbch\af14\loch\f2 freedom to share and change it.  By contrast, the GNU General Public
\par \hich\af2\dbch\af14\loch\f2 Licenses are intended to guarantee your freedom to share and change
\par \hich\af2\dbch\af14\loch\f2 free software--to make sure the software is free \hich\af2\dbch\af14\loch\f2 for all its users.
\par 
\par \hich\af2\dbch\af14\loch\f2   This license, the Lesser General Public License, applies to some
\par \hich\af2\dbch\af14\loch\f2 specially designated software packages--typically libraries--of the
\par \hich\af2\dbch\af14\loch\f2 Free Software Foundation and other authors who decide to use it.  You
\par \hich\af2\dbch\af14\loch\f2 can use it too, but we sug\hich\af2\dbch\af14\loch\f2 gest you first think carefully about whether
\par \hich\af2\dbch\af14\loch\f2 this license or the ordinary General Public License is the better
\par \hich\af2\dbch\af14\loch\f2 strategy to use in any particular case, based on the explanations below.
\par 
\par \hich\af2\dbch\af14\loch\f2   When we speak of free software, we are referring to freedom of us\hich\af2\dbch\af14\loch\f2 e,
\par \hich\af2\dbch\af14\loch\f2 not price.  Our General Public Licenses are designed to make sure that
\par \hich\af2\dbch\af14\loch\f2 you have the freedom to distribute copies of free software (and charge
\par \hich\af2\dbch\af14\loch\f2 for this service if you wish); that you receive source code or can get
\par \hich\af2\dbch\af14\loch\f2 it if you want it; that you can chan\hich\af2\dbch\af14\loch\f2 ge the software and use pieces of
\par \hich\af2\dbch\af14\loch\f2 it in new free programs; and that you are informed that you can do
\par \hich\af2\dbch\af14\loch\f2 these things.
\par 
\par \hich\af2\dbch\af14\loch\f2   To protect your rights, we need to make restrictions that forbid
\par \hich\af2\dbch\af14\loch\f2 distributors to deny you these rights or to ask you to surrender the\hich\af2\dbch\af14\loch\f2 se
\par \hich\af2\dbch\af14\loch\f2 rights.  These restrictions translate to certain responsibilities for
\par \hich\af2\dbch\af14\loch\f2 you if you distribute copies of the library or if you modify it.
\par 
\par \hich\af2\dbch\af14\loch\f2   For example, if you distribute copies of the library, whether gratis
\par \hich\af2\dbch\af14\loch\f2 or for a fee, you must give the recipient\hich\af2\dbch\af14\loch\f2 s all the rights that we gave
\par \hich\af2\dbch\af14\loch\f2 you.  You must make sure that they, too, receive or can get the source
\par \hich\af2\dbch\af14\loch\f2 code.  If you link other code with the library, you must provide
\par \hich\af2\dbch\af14\loch\f2 complete object files to the recipients, so that they can relink them
\par \hich\af2\dbch\af14\loch\f2 with the library\hich\af2\dbch\af14\loch\f2  after making changes to the library and recompiling
\par \hich\af2\dbch\af14\loch\f2 it.  And you must show them these terms so they know their rights.
\par 
\par \hich\af2\dbch\af14\loch\f2   We protect your rights with a two-step method: (1) we copyright the
\par \hich\af2\dbch\af14\loch\f2 library, and (2) we offer you this license, which gives you l\hich\af2\dbch\af14\loch\f2 egal
\par \hich\af2\dbch\af14\loch\f2 permission to copy, distribute and/or modify the library.
\par 
\par \hich\af2\dbch\af14\loch\f2   To protect each distributor, we want to make it very clear that
\par \hich\af2\dbch\af14\loch\f2 there is no warranty for the free library.  Also, if the library is
\par \hich\af2\dbch\af14\loch\f2 modified by someone else and passed on, the recipient\hich\af2\dbch\af14\loch\f2 s should know
\par \hich\af2\dbch\af14\loch\f2 that what they have is not the original version, so that the original
\par \hich\af2\dbch\af14\loch\f2 author's reputation will not be affected by problems that might be
\par \hich\af2\dbch\af14\loch\f2 introduced by others.
\par \page 
\par \hich\af2\dbch\af14\loch\f2   Finally, software patents pose a constant threat to the existence of
\par \hich\af2\dbch\af14\loch\f2 any \hich\af2\dbch\af14\loch\f2 free program.  We wish to make sure that a company cannot
\par \hich\af2\dbch\af14\loch\f2 effectively restrict the users of a free program by obtaining a
\par \hich\af2\dbch\af14\loch\f2 restrictive license from a patent holder.  Therefore, we insist that
\par \hich\af2\dbch\af14\loch\f2 any patent license obtained for a version of the library must \hich\af2\dbch\af14\loch\f2 be
\par \hich\af2\dbch\af14\loch\f2 consistent with the full freedom of use specified in this license.
\par 
\par \hich\af2\dbch\af14\loch\f2   Most GNU software, including some libraries, is covered by the
\par \hich\af2\dbch\af14\loch\f2 ordinary GNU General Public License.  This license, the GNU Lesser
\par \hich\af2\dbch\af14\loch\f2 General Public License, applies to certain desig\hich\af2\dbch\af14\loch\f2 nated libraries, and
\par \hich\af2\dbch\af14\loch\f2 is quite different from the ordinary General Public License.  We use
\par \hich\af2\dbch\af14\loch\f2 this license for certain libraries in order to permit linking those
\par \hich\af2\dbch\af14\loch\f2 libraries into non-free programs.
\par 
\par \hich\af2\dbch\af14\loch\f2   When a program is linked with a library, whether statica\hich\af2\dbch\af14\loch\f2 lly or using
\par \hich\af2\dbch\af14\loch\f2 a shared library, the combination of the two is legally speaking a
\par \hich\af2\dbch\af14\loch\f2 combined work, a derivative of the original library.  The ordinary
\par \hich\af2\dbch\af14\loch\f2 General Public License therefore permits such linking only if the
\par \hich\af2\dbch\af14\loch\f2 entire combination fits its criteria of\hich\af2\dbch\af14\loch\f2  freedom.  The Lesser General
\par \hich\af2\dbch\af14\loch\f2 Public License permits more lax criteria for linking other code with
\par \hich\af2\dbch\af14\loch\f2 the library.
\par 
\par \hich\af2\dbch\af14\loch\f2   We call this license the "Lesser" General Public License because it
\par \hich\af2\dbch\af14\loch\f2 does Less to protect the user's freedom than the ordinary General
\par \hich\af2\dbch\af14\loch\f2 P\hich\af2\dbch\af14\loch\f2 ublic License.  It also provides other free software developers Less
\par \hich\af2\dbch\af14\loch\f2 of an advantage over competing non-free programs.  These disadvantages
\par \hich\af2\dbch\af14\loch\f2 are the reason we use the ordinary General Public License for many
\par \hich\af2\dbch\af14\loch\f2 libraries.  However, the Lesser license provid\hich\af2\dbch\af14\loch\f2 es advantages in certain
\par \hich\af2\dbch\af14\loch\f2 special circumstances.
\par 
\par \hich\af2\dbch\af14\loch\f2   For example, on rare occasions, there may be a special need to
\par \hich\af2\dbch\af14\loch\f2 encourage the widest possible use of a certain library, so that it becomes
\par \hich\af2\dbch\af14\loch\f2 a de-facto standard.  To achieve this, non-free programs must \hich\af2\dbch\af14\loch\f2 be
\par \hich\af2\dbch\af14\loch\f2 allowed to use the library.  A more frequent case is that a free
\par \hich\af2\dbch\af14\loch\f2 library does the same job as widely used non-free libraries.  In this
\par \hich\af2\dbch\af14\loch\f2 case, there is little to gain by limiting the free library to free
\par \hich\af2\dbch\af14\loch\f2 software only, so we use the Lesser General Pub\hich\af2\dbch\af14\loch\f2 lic License.
\par 
\par \hich\af2\dbch\af14\loch\f2   In other cases, permission to use a particular library in non-free
\par \hich\af2\dbch\af14\loch\f2 programs enables a greater number of people to use a large body of
\par \hich\af2\dbch\af14\loch\f2 free software.  For example, permission to use the GNU C Library in
\par \hich\af2\dbch\af14\loch\f2 non-free programs enables many mo\hich\af2\dbch\af14\loch\f2 re people to use the whole GNU
\par \hich\af2\dbch\af14\loch\f2 operating system, as well as its variant, the GNU/Linux operating
\par \hich\af2\dbch\af14\loch\f2 system.
\par 
\par \hich\af2\dbch\af14\loch\f2   Although the Lesser General Public License is Less protective of the
\par \hich\af2\dbch\af14\loch\f2 users' freedom, it does ensure that the user of a program that is
\par \hich\af2\dbch\af14\loch\f2 linked \hich\af2\dbch\af14\loch\f2 with the Library has the freedom and the wherewithal to run
\par \hich\af2\dbch\af14\loch\f2 that program using a modified version of the Library.
\par 
\par \hich\af2\dbch\af14\loch\f2   The precise terms and conditions for copying, distribution and
\par \hich\af2\dbch\af14\loch\f2 modification follow.  Pay close attention to the difference between a
\par \hich\af2\dbch\af14\loch\f2 "\hich\af2\dbch\af14\loch\f2 work based on the library" and a "work that uses the library".  The
\par \hich\af2\dbch\af14\loch\f2 former contains code derived from the library, whereas the latter must
\par \hich\af2\dbch\af14\loch\f2 be combined with the library in order to run.
\par \page 
\par \tab \tab \hich\af2\dbch\af14\loch\f2   GNU LESSER GENERAL PUBLIC LICENSE
\par \hich\af2\dbch\af14\loch\f2    TERMS AND CONDITIONS FO\hich\af2\dbch\af14\loch\f2 R COPYING, DISTRIBUTION AND MODIFICATION
\par 
\par \hich\af2\dbch\af14\loch\f2   0. This License Agreement applies to any software library or other
\par \hich\af2\dbch\af14\loch\f2 program which contains a notice placed by the copyright holder or
\par \hich\af2\dbch\af14\loch\f2 other authorized party saying it may be distributed under the terms of
\par \hich\af2\dbch\af14\loch\f2 thi\hich\af2\dbch\af14\loch\f2 s Lesser General Public License (also called "this License").
\par \hich\af2\dbch\af14\loch\f2 Each licensee is addressed as "you".
\par 
\par \hich\af2\dbch\af14\loch\f2   A "library" means a collection of software functions and/or data
\par \hich\af2\dbch\af14\loch\f2 prepared so as to be conveniently linked with application programs
\par \hich\af2\dbch\af14\loch\f2 (which use some o\hich\af2\dbch\af14\loch\f2 f those functions and data) to form executables.
\par 
\par \hich\af2\dbch\af14\loch\f2   The "Library", below, refers to any such software library or work
\par \hich\af2\dbch\af14\loch\f2 which has been distributed under these terms.  A "work based on the
\par \hich\af2\dbch\af14\loch\f2 Library" means either the Library or any derivative work under
\par \hich\af2\dbch\af14\loch\f2 co\hich\af2\dbch\af14\loch\f2 pyright law: that is to say, a work containing the Library or a
\par \hich\af2\dbch\af14\loch\f2 portion of it, either verbatim or with modifications and/or translated
\par \hich\af2\dbch\af14\loch\f2 straightforwardly into another language.  (Hereinafter, translation is
\par \hich\af2\dbch\af14\loch\f2 included without limitation in the term "modifi\hich\af2\dbch\af14\loch\f2 cation".)
\par 
\par \hich\af2\dbch\af14\loch\f2   "Source code" for a work means the preferred form of the work for
\par \hich\af2\dbch\af14\loch\f2 making modifications to it.  For a library, complete source code means
\par \hich\af2\dbch\af14\loch\f2 all the source code for all modules it contains, plus any associated
\par \hich\af2\dbch\af14\loch\f2 interface definition files, plus\hich\af2\dbch\af14\loch\f2  the scripts used to control compilation
\par \hich\af2\dbch\af14\loch\f2 and installation of the library.
\par 
\par \hich\af2\dbch\af14\loch\f2   Activities other than copying, distribution and modification are not
\par \hich\af2\dbch\af14\loch\f2 covered by this License; they are outside its scope.  The act of
\par \hich\af2\dbch\af14\loch\f2 running a program using the Library is n\hich\af2\dbch\af14\loch\f2 ot restricted, and output from
\par \hich\af2\dbch\af14\loch\f2 such a program is covered only if its contents constitute a work based
\par \hich\af2\dbch\af14\loch\f2 on the Library (independent of the use of the Library in a tool for
\par \hich\af2\dbch\af14\loch\f2 writing it).  Whether that is true depends on what the Library does
\par \hich\af2\dbch\af14\loch\f2 and what the p\hich\af2\dbch\af14\loch\f2 rogram that uses the Library does.
\par \hich\af2\dbch\af14\loch\f2   
\par \hich\af2\dbch\af14\loch\f2   1. You may copy and distribute verbatim copies of the Library's
\par \hich\af2\dbch\af14\loch\f2 complete source code as you receive it, in any medium, provided that
\par \hich\af2\dbch\af14\loch\f2 you conspicuously and appropriately publish on each copy an
\par \hich\af2\dbch\af14\loch\f2 appropriate copyri\hich\af2\dbch\af14\loch\f2 ght notice and disclaimer of warranty; keep intact
\par \hich\af2\dbch\af14\loch\f2 all the notices that refer to this License and to the absence of any
\par \hich\af2\dbch\af14\loch\f2 warranty; and distribute a copy of this License along with the
\par \hich\af2\dbch\af14\loch\f2 Library.
\par 
\par \hich\af2\dbch\af14\loch\f2   You may charge a fee for the physical act of transferrin\hich\af2\dbch\af14\loch\f2 g a copy,
\par \hich\af2\dbch\af14\loch\f2 and you may at your option offer warranty protection in exchange for a
\par \hich\af2\dbch\af14\loch\f2 fee.
\par \page 
\par \hich\af2\dbch\af14\loch\f2   2. You may modify your copy or copies of the Library or any portion
\par \hich\af2\dbch\af14\loch\f2 of it, thus forming a work based on the Library, and copy and
\par \hich\af2\dbch\af14\loch\f2 distribute such modifications \hich\af2\dbch\af14\loch\f2 or work under the terms of Section 1
\par \hich\af2\dbch\af14\loch\f2 above, provided that you also meet all of these conditions:
\par 
\par \hich\af2\dbch\af14\loch\f2     a) The modified work must itself be a software library.
\par 
\par \hich\af2\dbch\af14\loch\f2     b) You must cause the files modified to carry prominent notices
\par \hich\af2\dbch\af14\loch\f2     stating that you ch\hich\af2\dbch\af14\loch\f2 anged the files and the date of any change.
\par 
\par \hich\af2\dbch\af14\loch\f2     c) You must cause the whole of the work to be licensed at no
\par \hich\af2\dbch\af14\loch\f2     charge to all third parties under the terms of this License.
\par 
\par \hich\af2\dbch\af14\loch\f2     d) If a facility in the modified Library refers to a function or a
\par \hich\af2\dbch\af14\loch\f2    \hich\af2\dbch\af14\loch\f2  table of data to be supplied by an application program that uses
\par \hich\af2\dbch\af14\loch\f2     the facility, other than as an argument passed when the facility
\par \hich\af2\dbch\af14\loch\f2     is invoked, then you must make a good faith effort to ensure that,
\par \hich\af2\dbch\af14\loch\f2     in the event an application does not supply\hich\af2\dbch\af14\loch\f2  such function or
\par \hich\af2\dbch\af14\loch\f2     table, the facility still operates, and performs whatever part of
\par \hich\af2\dbch\af14\loch\f2     its purpose remains meaningful.
\par 
\par \hich\af2\dbch\af14\loch\f2     (For example, a function in a library to compute square roots has
\par \hich\af2\dbch\af14\loch\f2     a purpose that is entirely well-defined independent \hich\af2\dbch\af14\loch\f2 of the
\par \hich\af2\dbch\af14\loch\f2     application.  Therefore, Subsection 2d requires that any
\par \hich\af2\dbch\af14\loch\f2     application-supplied function or table used by this function must
\par \hich\af2\dbch\af14\loch\f2     be optional: if the application does not supply it, the square
\par \hich\af2\dbch\af14\loch\f2     root function must still compute square roo\hich\af2\dbch\af14\loch\f2 ts.)
\par 
\par \hich\af2\dbch\af14\loch\f2 These requirements apply to the modified work as a whole.  If
\par \hich\af2\dbch\af14\loch\f2 identifiable sections of that work are not derived from the Library,
\par \hich\af2\dbch\af14\loch\f2 and can be reasonably considered independent and separate works in
\par \hich\af2\dbch\af14\loch\f2 themselves, then this License, and its terms, d\hich\af2\dbch\af14\loch\f2 o not apply to those
\par \hich\af2\dbch\af14\loch\f2 sections when you distribute them as separate works.  But when you
\par \hich\af2\dbch\af14\loch\f2 distribute the same sections as part of a whole which is a work based
\par \hich\af2\dbch\af14\loch\f2 on the Library, the distribution of the whole must be on the terms of
\par \hich\af2\dbch\af14\loch\f2 this License, whose perm\hich\af2\dbch\af14\loch\f2 issions for other licensees extend to the
\par \hich\af2\dbch\af14\loch\f2 entire whole, and thus to each and every part regardless of who wrote
\par \hich\af2\dbch\af14\loch\f2 it.
\par 
\par \hich\af2\dbch\af14\loch\f2 Thus, it is not the intent of this section to claim rights or contest
\par \hich\af2\dbch\af14\loch\f2 your rights to work written entirely by you; rather, the intent \hich\af2\dbch\af14\loch\f2 is to
\par \hich\af2\dbch\af14\loch\f2 exercise the right to control the distribution of derivative or
\par \hich\af2\dbch\af14\loch\f2 collective works based on the Library.
\par 
\par \hich\af2\dbch\af14\loch\f2 In addition, mere aggregation of another work not based on the Library
\par \hich\af2\dbch\af14\loch\f2 with the Library (or with a work based on the Library) on a volume of
\par \hich\af2\dbch\af14\loch\f2 a storage or distribution medium does not bring the other work under
\par \hich\af2\dbch\af14\loch\f2 the scope of this License.
\par 
\par \hich\af2\dbch\af14\loch\f2   3. You may opt to apply the terms of the ordinary GNU General Public
\par \hich\af2\dbch\af14\loch\f2 License instead of this License to a given copy of the Library.  To do
\par \hich\af2\dbch\af14\loch\f2 this, you m\hich\af2\dbch\af14\loch\f2 ust alter all the notices that refer to this License, so
\par \hich\af2\dbch\af14\loch\f2 that they refer to the ordinary GNU General Public License, version 2,
\par \hich\af2\dbch\af14\loch\f2 instead of to this License.  (If a newer version than version 2 of the
\par \hich\af2\dbch\af14\loch\f2 ordinary GNU General Public License has appeared, then\hich\af2\dbch\af14\loch\f2  you can specify
\par \hich\af2\dbch\af14\loch\f2 that version instead if you wish.)  Do not make any other change in
\par \hich\af2\dbch\af14\loch\f2 these notices.
\par \page 
\par \hich\af2\dbch\af14\loch\f2   Once this change is made in a given copy, it is irreversible for
\par \hich\af2\dbch\af14\loch\f2 that copy, so the ordinary GNU General Public License applies to all
\par \hich\af2\dbch\af14\loch\f2 subsequent c\hich\af2\dbch\af14\loch\f2 opies and derivative works made from that copy.
\par 
\par \hich\af2\dbch\af14\loch\f2   This option is useful when you wish to copy part of the code of
\par \hich\af2\dbch\af14\loch\f2 the Library into a program that is not a library.
\par 
\par \hich\af2\dbch\af14\loch\f2   4. You may copy and distribute the Library (or a portion or
\par \hich\af2\dbch\af14\loch\f2 derivative of it, unde\hich\af2\dbch\af14\loch\f2 r Section 2) in object code or executable form
\par \hich\af2\dbch\af14\loch\f2 under the terms of Sections 1 and 2 above provided that you accompany
\par \hich\af2\dbch\af14\loch\f2 it with the complete corresponding machine-readable source code, which
\par \hich\af2\dbch\af14\loch\f2 must be distributed under the terms of Sections 1 and 2 above on \hich\af2\dbch\af14\loch\f2 a
\par \hich\af2\dbch\af14\loch\f2 medium customarily used for software interchange.
\par 
\par \hich\af2\dbch\af14\loch\f2   If distribution of object code is made by offering access to copy
\par \hich\af2\dbch\af14\loch\f2 from a designated place, then offering equivalent access to copy the
\par \hich\af2\dbch\af14\loch\f2 source code from the same place satisfies the requirement to
\par \hich\af2\dbch\af14\loch\f2 distribute the source code, even though third parties are not
\par \hich\af2\dbch\af14\loch\f2 compelled to copy the source along with the object code.
\par 
\par \hich\af2\dbch\af14\loch\f2   5. A program that contains no derivative of any portion of the
\par \hich\af2\dbch\af14\loch\f2 Library, but is designed to work with the Library by being compile\hich\af2\dbch\af14\loch\f2 d or
\par \hich\af2\dbch\af14\loch\f2 linked with it, is called a "work that uses the Library".  Such a
\par \hich\af2\dbch\af14\loch\f2 work, in isolation, is not a derivative work of the Library, and
\par \hich\af2\dbch\af14\loch\f2 therefore falls outside the scope of this License.
\par 
\par \hich\af2\dbch\af14\loch\f2   However, linking a "work that uses the Library" with the Libr\hich\af2\dbch\af14\loch\f2 ary
\par \hich\af2\dbch\af14\loch\f2 creates an executable that is a derivative of the Library (because it
\par \hich\af2\dbch\af14\loch\f2 contains portions of the Library), rather than a "work that uses the
\par \hich\af2\dbch\af14\loch\f2 library".  The executable is therefore covered by this License.
\par \hich\af2\dbch\af14\loch\f2 Section 6 states terms for distribution of su\hich\af2\dbch\af14\loch\f2 ch executables.
\par 
\par \hich\af2\dbch\af14\loch\f2   When a "work that uses the Library" uses material from a header file
\par \hich\af2\dbch\af14\loch\f2 that is part of the Library, the object code for the work may be a
\par \hich\af2\dbch\af14\loch\f2 derivative work of the Library even though the source code is not.
\par \hich\af2\dbch\af14\loch\f2 Whether this is true is espec\hich\af2\dbch\af14\loch\f2 ially significant if the work can be
\par \hich\af2\dbch\af14\loch\f2 linked without the Library, or if the work is itself a library.  The
\par \hich\af2\dbch\af14\loch\f2 threshold for this to be true is not precisely defined by law.
\par 
\par \hich\af2\dbch\af14\loch\f2   If such an object file uses only numerical parameters, data
\par \hich\af2\dbch\af14\loch\f2 structure layouts a\hich\af2\dbch\af14\loch\f2 nd accessors, and small macros and small inline
\par \hich\af2\dbch\af14\loch\f2 functions (ten lines or less in length), then the use of the object
\par \hich\af2\dbch\af14\loch\f2 file is unrestricted, regardless of whether it is legally a derivative
\par \hich\af2\dbch\af14\loch\f2 work.  (Executables containing this object code plus portions of t\hich\af2\dbch\af14\loch\f2 he
\par \hich\af2\dbch\af14\loch\f2 Library will still fall under Section 6.)
\par 
\par \hich\af2\dbch\af14\loch\f2   Otherwise, if the work is a derivative of the Library, you may
\par \hich\af2\dbch\af14\loch\f2 distribute the object code for the work under the terms of Section 6.
\par \hich\af2\dbch\af14\loch\f2 Any executables containing that work also fall under Section 6,
\par \hich\af2\dbch\af14\loch\f2 wheth\hich\af2\dbch\af14\loch\f2 er or not they are linked directly with the Library itself.
\par \page 
\par \hich\af2\dbch\af14\loch\f2   6. As an exception to the Sections above, you may also combine or
\par \hich\af2\dbch\af14\loch\f2 link a "work that uses the Library" with the Library to produce a
\par \hich\af2\dbch\af14\loch\f2 work containing portions of the Library, and distribute \hich\af2\dbch\af14\loch\f2 that work
\par \hich\af2\dbch\af14\loch\f2 under terms of your choice, provided that the terms permit
\par \hich\af2\dbch\af14\loch\f2 modification of the work for the customer's own use and reverse
\par \hich\af2\dbch\af14\loch\f2 engineering for debugging such modifications.
\par 
\par \hich\af2\dbch\af14\loch\f2   You must give prominent notice with each copy of the work that the
\par \hich\af2\dbch\af14\loch\f2 L\hich\af2\dbch\af14\loch\f2 ibrary is used in it and that the Library and its use are covered by
\par \hich\af2\dbch\af14\loch\f2 this License.  You must supply a copy of this License.  If the work
\par \hich\af2\dbch\af14\loch\f2 during execution displays copyright notices, you must include the
\par \hich\af2\dbch\af14\loch\f2 copyright notice for the Library among them, as we\hich\af2\dbch\af14\loch\f2 ll as a reference
\par \hich\af2\dbch\af14\loch\f2 directing the user to the copy of this License.  Also, you must do one
\par \hich\af2\dbch\af14\loch\f2 of these things:
\par 
\par \hich\af2\dbch\af14\loch\f2     a) Accompany the work with the complete corresponding
\par \hich\af2\dbch\af14\loch\f2     machine-readable source code for the Library including whatever
\par \hich\af2\dbch\af14\loch\f2     changes were \hich\af2\dbch\af14\loch\f2 used in the work (which must be distributed under
\par \hich\af2\dbch\af14\loch\f2     Sections 1 and 2 above); and, if the work is an executable linked
\par \hich\af2\dbch\af14\loch\f2     with the Library, with the complete machine-readable "work that
\par \hich\af2\dbch\af14\loch\f2     uses the Library", as object code and/or source code, so that\hich\af2\dbch\af14\loch\f2  the
\par \hich\af2\dbch\af14\loch\f2     user can modify the Library and then relink to produce a modified
\par \hich\af2\dbch\af14\loch\f2     executable containing the modified Library.  (It is understood
\par \hich\af2\dbch\af14\loch\f2     that the user who changes the contents of definitions files in the
\par \hich\af2\dbch\af14\loch\f2     Library will not necessarily be abl\hich\af2\dbch\af14\loch\f2 e to recompile the application
\par \hich\af2\dbch\af14\loch\f2     to use the modified definitions.)
\par 
\par \hich\af2\dbch\af14\loch\f2     b) Use a suitable shared library mechanism for linking with the
\par \hich\af2\dbch\af14\loch\f2     Library.  A suitable mechanism is one that (1) uses at run time a
\par \hich\af2\dbch\af14\loch\f2     copy of the library already present on \hich\af2\dbch\af14\loch\f2 the user's computer system,
\par \hich\af2\dbch\af14\loch\f2     rather than copying library functions into the executable, and (2)
\par \hich\af2\dbch\af14\loch\f2     will operate properly with a modified version of the library, if
\par \hich\af2\dbch\af14\loch\f2     the user installs one, as long as the modified version is
\par \hich\af2\dbch\af14\loch\f2     interface-compatib\hich\af2\dbch\af14\loch\f2 le with the version that the work was made with.
\par 
\par \hich\af2\dbch\af14\loch\f2     c) Accompany the work with a written offer, valid for at
\par \hich\af2\dbch\af14\loch\f2     least three years, to give the same user the materials
\par \hich\af2\dbch\af14\loch\f2     specified in Subsection 6a, above, for a charge no more
\par \hich\af2\dbch\af14\loch\f2     than the cost of \hich\af2\dbch\af14\loch\f2 performing this distribution.
\par 
\par \hich\af2\dbch\af14\loch\f2     d) If distribution of the work is made by offering access to copy
\par \hich\af2\dbch\af14\loch\f2     from a designated place, offer equivalent access to copy the above
\par \hich\af2\dbch\af14\loch\f2     specified materials from the same place.
\par 
\par \hich\af2\dbch\af14\loch\f2     e) Verify that the user has \hich\af2\dbch\af14\loch\f2 already received a copy of these
\par \hich\af2\dbch\af14\loch\f2     materials or that you have already sent this user a copy.
\par 
\par \hich\af2\dbch\af14\loch\f2   For an executable, the required form of the "work that uses the
\par \hich\af2\dbch\af14\loch\f2 Library" must include any data and utility programs needed for
\par \hich\af2\dbch\af14\loch\f2 reproducing the executable\hich\af2\dbch\af14\loch\f2  from it.  However, as a special exception,
\par \hich\af2\dbch\af14\loch\f2 the materials to be distributed need not include anything that is
\par \hich\af2\dbch\af14\loch\f2 normally distributed (in either source or binary form) with the major
\par \hich\af2\dbch\af14\loch\f2 components (compiler, kernel, and so on) of the operating system on
\par \hich\af2\dbch\af14\loch\f2 whic\hich\af2\dbch\af14\loch\f2 h the executable runs, unless that component itself accompanies
\par \hich\af2\dbch\af14\loch\f2 the executable.
\par 
\par \hich\af2\dbch\af14\loch\f2   It may happen that this requirement contradicts the license
\par \hich\af2\dbch\af14\loch\f2 restrictions of other proprietary libraries that do not normally
\par \hich\af2\dbch\af14\loch\f2 accompany the operating system.  Such a con\hich\af2\dbch\af14\loch\f2 tradiction means you cannot
\par \hich\af2\dbch\af14\loch\f2 use both them and the Library together in an executable that you
\par \hich\af2\dbch\af14\loch\f2 distribute.
\par \page 
\par \hich\af2\dbch\af14\loch\f2   7. You may place library facilities that are a work based on the
\par \hich\af2\dbch\af14\loch\f2 Library side-by-side in a single library together with other library
\par \hich\af2\dbch\af14\loch\f2 facilit\hich\af2\dbch\af14\loch\f2 ies not covered by this License, and distribute such a combined
\par \hich\af2\dbch\af14\loch\f2 library, provided that the separate distribution of the work based on
\par \hich\af2\dbch\af14\loch\f2 the Library and of the other library facilities is otherwise
\par \hich\af2\dbch\af14\loch\f2 permitted, and provided that you do these two things:
\par 
\par \hich\af2\dbch\af14\loch\f2  \hich\af2\dbch\af14\loch\f2    a) Accompany the combined library with a copy of the same work
\par \hich\af2\dbch\af14\loch\f2     based on the Library, uncombined with any other library
\par \hich\af2\dbch\af14\loch\f2     facilities.  This must be distributed under the terms of the
\par \hich\af2\dbch\af14\loch\f2     Sections above.
\par 
\par \hich\af2\dbch\af14\loch\f2     b) Give prominent notice with the c\hich\af2\dbch\af14\loch\f2 ombined library of the fact
\par \hich\af2\dbch\af14\loch\f2     that part of it is a work based on the Library, and explaining
\par \hich\af2\dbch\af14\loch\f2     where to find the accompanying uncombined form of the same work.
\par 
\par \hich\af2\dbch\af14\loch\f2   8. You may not copy, modify, sublicense, link with, or distribute
\par \hich\af2\dbch\af14\loch\f2 the Library except\hich\af2\dbch\af14\loch\f2  as expressly provided under this License.  Any
\par \hich\af2\dbch\af14\loch\f2 attempt otherwise to copy, modify, sublicense, link with, or
\par \hich\af2\dbch\af14\loch\f2 distribute the Library is void, and will automatically terminate your
\par \hich\af2\dbch\af14\loch\f2 rights under this License.  However, parties who have received copies,
\par \hich\af2\dbch\af14\loch\f2 or\hich\af2\dbch\af14\loch\f2  rights, from you under this License will not have their licenses
\par \hich\af2\dbch\af14\loch\f2 terminated so long as such parties remain in full compliance.
\par 
\par \hich\af2\dbch\af14\loch\f2   9. You are not required to accept this License, since you have not
\par \hich\af2\dbch\af14\loch\f2 signed it.  However, nothing else grants you permissio\hich\af2\dbch\af14\loch\f2 n to modify or
\par \hich\af2\dbch\af14\loch\f2 distribute the Library or its derivative works.  These actions are
\par \hich\af2\dbch\af14\loch\f2 prohibited by law if you do not accept this License.  Therefore, by
\par \hich\af2\dbch\af14\loch\f2 modifying or distributing the Library (or any work based on the
\par \hich\af2\dbch\af14\loch\f2 Library), you indicate your acceptance\hich\af2\dbch\af14\loch\f2  of this License to do so, and
\par \hich\af2\dbch\af14\loch\f2 all its terms and conditions for copying, distributing or modifying
\par \hich\af2\dbch\af14\loch\f2 the Library or works based on it.
\par 
\par \hich\af2\dbch\af14\loch\f2   10. Each time you redistribute the Library (or any work based on the
\par \hich\af2\dbch\af14\loch\f2 Library), the recipient automatically receives\hich\af2\dbch\af14\loch\f2  a license from the
\par \hich\af2\dbch\af14\loch\f2 original licensor to copy, distribute, link with or modify the Library
\par \hich\af2\dbch\af14\loch\f2 subject to these terms and conditions.  You may not impose any further
\par \hich\af2\dbch\af14\loch\f2 restrictions on the recipients' exercise of the rights granted herein.
\par \hich\af2\dbch\af14\loch\f2 You are not respons\hich\af2\dbch\af14\loch\f2 ible for enforcing compliance by third parties with
\par \hich\af2\dbch\af14\loch\f2 this License.
\par \page 
\par \hich\af2\dbch\af14\loch\f2   11. If, as a consequence of a court judgment or allegation of patent
\par \hich\af2\dbch\af14\loch\f2 infringement or for any other reason (not limited to patent issues),
\par \hich\af2\dbch\af14\loch\f2 conditions are imposed on you (whether by c\hich\af2\dbch\af14\loch\f2 ourt order, agreement or
\par \hich\af2\dbch\af14\loch\f2 otherwise) that contradict the conditions of this License, they do not
\par \hich\af2\dbch\af14\loch\f2 excuse you from the conditions of this License.  If you cannot
\par \hich\af2\dbch\af14\loch\f2 distribute so as to satisfy simultaneously your obligations under this
\par \hich\af2\dbch\af14\loch\f2 License and any other \hich\af2\dbch\af14\loch\f2 pertinent obligations, then as a consequence you
\par \hich\af2\dbch\af14\loch\f2 may not distribute the Library at all.  For example, if a patent
\par \hich\af2\dbch\af14\loch\f2 license would not permit royalty-free redistribution of the Library by
\par \hich\af2\dbch\af14\loch\f2 all those who receive copies directly or indirectly through you, the\hich\af2\dbch\af14\loch\f2 n
\par \hich\af2\dbch\af14\loch\f2 the only way you could satisfy both it and this License would be to
\par \hich\af2\dbch\af14\loch\f2 refrain entirely from distribution of the Library.
\par 
\par \hich\af2\dbch\af14\loch\f2 If any portion of this section is held invalid or unenforceable under any
\par \hich\af2\dbch\af14\loch\f2 particular circumstance, the balance of the section is \hich\af2\dbch\af14\loch\f2 intended to apply,
\par \hich\af2\dbch\af14\loch\f2 and the section as a whole is intended to apply in other circumstances.
\par 
\par \hich\af2\dbch\af14\loch\f2 It is not the purpose of this section to induce you to infringe any
\par \hich\af2\dbch\af14\loch\f2 patents or other property right claims or to contest validity of any
\par \hich\af2\dbch\af14\loch\f2 such claims; this sect\hich\af2\dbch\af14\loch\f2 ion has the sole purpose of protecting the
\par \hich\af2\dbch\af14\loch\f2 integrity of the free software distribution system which is
\par \hich\af2\dbch\af14\loch\f2 implemented by public license practices.  Many people have made
\par \hich\af2\dbch\af14\loch\f2 generous contributions to the wide range of software distributed
\par \hich\af2\dbch\af14\loch\f2 through that system \hich\af2\dbch\af14\loch\f2 in reliance on consistent application of that
\par \hich\af2\dbch\af14\loch\f2 system; it is up to the author/donor to decide if he or she is willing
\par \hich\af2\dbch\af14\loch\f2 to distribute software through any other system and a licensee cannot
\par \hich\af2\dbch\af14\loch\f2 impose that choice.
\par 
\par \hich\af2\dbch\af14\loch\f2 This section is intended to make thoroughly\hich\af2\dbch\af14\loch\f2  clear what is believed to
\par \hich\af2\dbch\af14\loch\f2 be a consequence of the rest of this License.
\par 
\par \hich\af2\dbch\af14\loch\f2   12. If the distribution and/or use of the Library is restricted in
\par \hich\af2\dbch\af14\loch\f2 certain countries either by patents or by copyrighted interfaces, the
\par \hich\af2\dbch\af14\loch\f2 original copyright holder who places t\hich\af2\dbch\af14\loch\f2 he Library under this License may add
\par \hich\af2\dbch\af14\loch\f2 an explicit geographical distribution limitation excluding those countries,
\par \hich\af2\dbch\af14\loch\f2 so that distribution is permitted only in or among countries not thus
\par \hich\af2\dbch\af14\loch\f2 excluded.  In such case, this License incorporates the limitation as \hich\af2\dbch\af14\loch\f2 if
\par \hich\af2\dbch\af14\loch\f2 written in the body of this License.
\par 
\par \hich\af2\dbch\af14\loch\f2   13. The Free Software Foundation may publish revised and/or new
\par \hich\af2\dbch\af14\loch\f2 versions of the Lesser General Public License from time to time.
\par \hich\af2\dbch\af14\loch\f2 Such new versions will be similar in spirit to the present version,
\par \hich\af2\dbch\af14\loch\f2 but may di\hich\af2\dbch\af14\loch\f2 ffer in detail to address new problems or concerns.
\par 
\par \hich\af2\dbch\af14\loch\f2 Each version is given a distinguishing version number.  If the Library
\par \hich\af2\dbch\af14\loch\f2 specifies a version number of this License which applies to it and
\par \hich\af2\dbch\af14\loch\f2 "any later version", you have the option of following the ter\hich\af2\dbch\af14\loch\f2 ms and
\par \hich\af2\dbch\af14\loch\f2 conditions either of that version or of any later version published by
\par \hich\af2\dbch\af14\loch\f2 the Free Software Foundation.  If the Library does not specify a
\par \hich\af2\dbch\af14\loch\f2 license version number, you may choose any version ever published by
\par \hich\af2\dbch\af14\loch\f2 the Free Software Foundation.
\par \page 
\par \hich\af2\dbch\af14\loch\f2   14. \hich\af2\dbch\af14\loch\f2 If you wish to incorporate parts of the Library into other free
\par \hich\af2\dbch\af14\loch\f2 programs whose distribution conditions are incompatible with these,
\par \hich\af2\dbch\af14\loch\f2 write to the author to ask for permission.  For software which is
\par \hich\af2\dbch\af14\loch\f2 copyrighted by the Free Software Foundation, write to t\hich\af2\dbch\af14\loch\f2 he Free
\par \hich\af2\dbch\af14\loch\f2 Software Foundation; we sometimes make exceptions for this.  Our
\par \hich\af2\dbch\af14\loch\f2 decision will be guided by the two goals of preserving the free status
\par \hich\af2\dbch\af14\loch\f2 of all derivatives of our free software and of promoting the sharing
\par \hich\af2\dbch\af14\loch\f2 and reuse of software generally.
\par 
\par \tab \tab \tab \hich\af2\dbch\af14\loch\f2     NO WARRANTY
\par 
\par \hich\af2\dbch\af14\loch\f2   15. BECAUSE THE LIBRARY IS LICENSED FREE OF CHARGE, THERE IS NO
\par \hich\af2\dbch\af14\loch\f2 WARRANTY FOR THE LIBRARY, TO THE EXTENT PERMITTED BY APPLICABLE LAW.
\par \hich\af2\dbch\af14\loch\f2 EXCEPT WHEN OTHERWISE STATED IN WRITING THE COPYRIGHT HOLDERS AND/OR
\par \hich\af2\dbch\af14\loch\f2 OTHER PARTIES PROVIDE THE LIBR\hich\af2\dbch\af14\loch\f2 ARY "AS IS" WITHOUT WARRANTY OF ANY
\par \hich\af2\dbch\af14\loch\f2 KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, THE
\par \hich\af2\dbch\af14\loch\f2 IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
\par \hich\af2\dbch\af14\loch\f2 PURPOSE.  THE ENTIRE RISK AS TO THE QUALITY AND PERFORMANCE OF THE
\par \hich\af2\dbch\af14\loch\f2 LIBRARY IS W\hich\af2\dbch\af14\loch\f2 ITH YOU.  SHOULD THE LIBRARY PROVE DEFECTIVE, YOU ASSUME
\par \hich\af2\dbch\af14\loch\f2 THE COST OF ALL NECESSARY SERVICING, REPAIR OR CORRECTION.
\par 
\par \hich\af2\dbch\af14\loch\f2   16. IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN
\par \hich\af2\dbch\af14\loch\f2 WRITING WILL ANY COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MAY MODIF\hich\af2\dbch\af14\loch\f2 Y
\par \hich\af2\dbch\af14\loch\f2 AND/OR REDISTRIBUTE THE LIBRARY AS PERMITTED ABOVE, BE LIABLE TO YOU
\par \hich\af2\dbch\af14\loch\f2 FOR DAMAGES, INCLUDING ANY GENERAL, SPECIAL, INCIDENTAL OR
\par \hich\af2\dbch\af14\loch\f2 CONSEQUENTIAL DAMAGES ARISING OUT OF THE USE OR INABILITY TO USE THE
\par \hich\af2\dbch\af14\loch\f2 LIBRARY (INCLUDING BUT NOT LIMITED TO LOSS OF DATA OR\hich\af2\dbch\af14\loch\f2  DATA BEING
\par \hich\af2\dbch\af14\loch\f2 RENDERED INACCURATE OR LOSSES SUSTAINED BY YOU OR THIRD PARTIES OR A
\par \hich\af2\dbch\af14\loch\f2 FAILURE OF THE LIBRARY TO OPERATE WITH ANY OTHER SOFTWARE), EVEN IF
\par \hich\af2\dbch\af14\loch\f2 SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH
\par \hich\af2\dbch\af14\loch\f2 DAMAGES.
\par 
\par \tab \tab \hich\af2\dbch\af14\loch\f2      END OF TERMS \hich\af2\dbch\af14\loch\f2 AND CONDITIONS
\par \page 
\par \hich\af2\dbch\af14\loch\f2            How to Apply These Terms to Your New Libraries
\par 
\par \hich\af2\dbch\af14\loch\f2   If you develop a new library, and you want it to be of the greatest
\par \hich\af2\dbch\af14\loch\f2 possible use to the public, we recommend making it free software that
\par \hich\af2\dbch\af14\loch\f2 everyone can redistribute and chan\hich\af2\dbch\af14\loch\f2 ge.  You can do so by permitting
\par \hich\af2\dbch\af14\loch\f2 redistribution under these terms (or, alternatively, under the terms of the
\par \hich\af2\dbch\af14\loch\f2 ordinary General Public License).
\par 
\par \hich\af2\dbch\af14\loch\f2   To apply these terms, attach the following notices to the library.  It is
\par \hich\af2\dbch\af14\loch\f2 safest to attach them to the st\hich\af2\dbch\af14\loch\f2 art of each source file to most effectively
\par \hich\af2\dbch\af14\loch\f2 convey the exclusion of warranty; and each file should have at least the
\par \hich\af2\dbch\af14\loch\f2 "copyright" line and a pointer to where the full notice is found.
\par 
\par \hich\af2\dbch\af14\loch\f2     <one line to give the library's name and a brief idea of what it\hich\af2\dbch\af14\loch\f2  does.>
\par \hich\af2\dbch\af14\loch\f2     Copyright (C) <year>  <name of author>
\par 
\par \hich\af2\dbch\af14\loch\f2     This library is free software; you can redistribute it and/or
\par \hich\af2\dbch\af14\loch\f2     modify it under the terms of the GNU Lesser General Public
\par \hich\af2\dbch\af14\loch\f2     License as published by the Free Software Foundation; either
\par \hich\af2\dbch\af14\loch\f2     \hich\af2\dbch\af14\loch\f2 version 2.1 of the License, or (at your option) any later version.
\par 
\par \hich\af2\dbch\af14\loch\f2     This library is distributed in the hope that it will be useful,
\par \hich\af2\dbch\af14\loch\f2     but WITHOUT ANY WARRANTY; without even the implied warranty of
\par \hich\af2\dbch\af14\loch\f2     MERCHANTABILITY or FITNESS FOR A PARTICULAR P\hich\af2\dbch\af14\loch\f2 URPOSE.  See the GNU
\par \hich\af2\dbch\af14\loch\f2     Lesser General Public License for more details.
\par 
\par \hich\af2\dbch\af14\loch\f2     You should have received a copy of the GNU Lesser General Public
\par \hich\af2\dbch\af14\loch\f2     License along with this library; if not, write to the Free Software
\par \hich\af2\dbch\af14\loch\f2     Foundation, Inc., 51 Franklin St\hich\af2\dbch\af14\loch\f2 , Fifth Floor, Boston, MA  02110-1301  USA
\par 
\par \hich\af2\dbch\af14\loch\f2 Also add information on how to contact you by electronic and paper mail.
\par 
\par \hich\af2\dbch\af14\loch\f2 You should also get your employer (if you work as a programmer) or your
\par \hich\af2\dbch\af14\loch\f2 school, if any, to sign a "copyright disclaimer" for the libr\hich\af2\dbch\af14\loch\f2 ary, if
\par \hich\af2\dbch\af14\loch\f2 necessary.  Here is a sample; alter the names:
\par 
\par \hich\af2\dbch\af14\loch\f2   Yoyodyne, Inc., hereby disclaims all copyright interest in the
\par \hich\af2\dbch\af14\loch\f2   library `Frob' (a library for tweaking knobs) written by James Random Hacker.
\par 
\par \hich\af2\dbch\af14\loch\f2   <signature of Ty Coon>, 1 April 1990
\par \hich\af2\dbch\af14\loch\f2   Ty Coo\hich\af2\dbch\af14\loch\f2 n, President of Vice
\par 
\par \hich\af2\dbch\af14\loch\f2 That's all there is to it!
\par 
\par }{\rtlch\fcs1 \af2 \ltrch\fcs0 \insrsid11168331 
\par }}