{\rtf1\adeflang1025\ansi\ansicpg1252\uc1\adeff0\deff0\stshfdbch14\stshfloch0\stshfhich0\stshfbi0\deflang1033\deflangfe1028{\fonttbl{\f0\froman\fcharset0\fprq2{\*\panose 02020603050405020304}Times New Roman;}{\f2\fmodern\fcharset0\fprq1{\*\panose 02070309020205020404}Courier New;}
{\f14\fnil\fcharset136\fprq2{\*\panose 02010601000101010101}PMingLiU{\*\falt Arial Unicode MS};}{\f153\fnil\fcharset136\fprq2{\*\panose 00000000000000000000}@PMingLiU;}{\f154\froman\fcharset238\fprq2 Times New Roman CE;}
{\f155\froman\fcharset204\fprq2 Times New Roman Cyr;}{\f157\froman\fcharset161\fprq2 Times New Roman Greek;}{\f158\froman\fcharset162\fprq2 Times New Roman Tur;}{\f159\fbidi \froman\fcharset177\fprq2 Times New Roman (Hebrew);}
{\f160\fbidi \froman\fcharset178\fprq2 Times New Roman (Arabic);}{\f161\froman\fcharset186\fprq2 Times New Roman Baltic;}{\f162\froman\fcharset163\fprq2 Times New Roman (Vietnamese);}{\f174\fmodern\fcharset238\fprq1 Courier New CE;}
{\f175\fmodern\fcharset204\fprq1 Courier New Cyr;}{\f177\fmodern\fcharset161\fprq1 Courier New Greek;}{\f178\fmodern\fcharset162\fprq1 Courier New Tur;}{\f179\fbidi \fmodern\fcharset177\fprq1 Courier New (Hebrew);}
{\f180\fbidi \fmodern\fcharset178\fprq1 Courier New (Arabic);}{\f181\fmodern\fcharset186\fprq1 Courier New Baltic;}{\f182\fmodern\fcharset163\fprq1 Courier New (Vietnamese);}}{\colortbl;\red0\green0\blue0;\red0\green0\blue255;\red0\green255\blue255;
\red0\green255\blue0;\red255\green0\blue255;\red255\green0\blue0;\red255\green255\blue0;\red255\green255\blue255;\red0\green0\blue128;\red0\green128\blue128;\red0\green128\blue0;\red128\green0\blue128;\red128\green0\blue0;\red128\green128\blue0;
\red128\green128\blue128;\red192\green192\blue192;}{\stylesheet{\ql \li0\ri0\widctlpar\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0 \rtlch\fcs1 \af0\afs24\alang1025 \ltrch\fcs0 
\fs24\lang1033\langfe1028\loch\f0\hich\af0\dbch\af14\cgrid\langnp1033\langfenp1028 \snext0 Normal;}{\*\cs10 \additive \ssemihidden Default Paragraph Font;}{\*
\ts11\tsrowd\trftsWidthB3\trpaddl108\trpaddr108\trpaddfl3\trpaddft3\trpaddfb3\trpaddfr3\trcbpat1\trcfpat1\tscellwidthfts0\tsvertalt\tsbrdrt\tsbrdrl\tsbrdrb\tsbrdrr\tsbrdrdgl\tsbrdrdgr\tsbrdrh\tsbrdrv 
\ql \li0\ri0\widctlpar\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0 \rtlch\fcs1 \af0\afs20 \ltrch\fcs0 \fs20\lang1024\langfe1024\loch\f0\hich\af0\dbch\af14\cgrid\langnp1024\langfenp1024 \snext11 \ssemihidden Normal Table;}{
\s15\ql \li0\ri0\widctlpar\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0 \rtlch\fcs1 \af2\afs20\alang1025 \ltrch\fcs0 \fs20\lang1033\langfe1028\loch\f2\hich\af2\dbch\af14\cgrid\langnp1033\langfenp1028 \sbasedon0 \snext15 \styrsid3754175 Plain Text;}}
{\*\latentstyles\lsdstimax156\lsdlockeddef0}{\*\rsidtbl \rsid474\rsid68730\rsid77744\rsid200972\rsid202343\rsid340389\rsid413488\rsid467166\rsid656002\rsid726551\rsid729287\rsid936952\rsid1004773\rsid1014356\rsid1265243\rsid1332399\rsid1589327\rsid1596877
\rsid1709434\rsid1781109\rsid1985473\rsid1985832\rsid1997227\rsid2036987\rsid2060446\rsid2164318\rsid2243883\rsid2253916\rsid2567185\rsid2567637\rsid2622546\rsid2777930\rsid2838836\rsid2964589\rsid3353643\rsid3565487\rsid3754175\rsid3804266\rsid3825468
\rsid4004439\rsid4130868\rsid4141992\rsid4338816\rsid4406752\rsid4603602\rsid4721804\rsid4813120\rsid4855380\rsid5267079\rsid5311350\rsid5323653\rsid5506723\rsid5509841\rsid5655055\rsid5716959\rsid5777070\rsid5784793\rsid5787125\rsid5794163\rsid5845810
\rsid5901623\rsid6231793\rsid6256615\rsid6301864\rsid6320269\rsid6366727\rsid6824997\rsid6969006\rsid7098056\rsid7100641\rsid7175781\rsid7292684\rsid7484807\rsid7634859\rsid7872531\rsid7934035\rsid7942967\rsid7995404\rsid8078353\rsid8401500\rsid8484561
\rsid8608306\rsid8655586\rsid8721664\rsid8735443\rsid8852293\rsid9047685\rsid9327835\rsid9336856\rsid9440601\rsid9516821\rsid9586365\rsid9642140\rsid9769593\rsid9906650\rsid9968313\rsid9992571\rsid10184217\rsid10230039\rsid10359293\rsid10365952
\rsid10581881\rsid10639865\rsid10834591\rsid10899037\rsid11164892\rsid11303133\rsid11403804\rsid11431521\rsid11545965\rsid11558645\rsid11560683\rsid11562399\rsid11564247\rsid11621421\rsid11676765\rsid11684437\rsid11750258\rsid11807751\rsid12060664
\rsid12079163\rsid12273124\rsid12340957\rsid12353219\rsid12593538\rsid12675781\rsid12733557\rsid12781090\rsid12870658\rsid13047990\rsid13071164\rsid13139276\rsid13201580\rsid13331796\rsid13388756\rsid13442406\rsid13839155\rsid13960660\rsid14112554
\rsid14164144\rsid14184705\rsid14366939\rsid14372153\rsid14497332\rsid14499682\rsid14578437\rsid14629096\rsid14631914\rsid14634800\rsid14680120\rsid14682807\rsid14751681\rsid14820212\rsid14899707\rsid14902208\rsid15081954\rsid15084235\rsid15354878
\rsid15408241\rsid15478159\rsid15564113\rsid15597980\rsid15731396\rsid15737555\rsid16004358\rsid16009437\rsid16187444\rsid16334942\rsid16522230\rsid16536646\rsid16545256\rsid16668395\rsid16737298}{\*\generator Microsoft Word 11.0.6359;}{\info
{\author Alan Williams}{\operator Alan Williams}{\creatim\yr2006\mo4\dy10\hr21\min46}{\revtim\yr2006\mo4\dy10\hr21\min46}{\version2}{\edmins0}{\nofpages7}{\nofwords2739}{\nofchars15616}{\*\company Affymetrix, Inc.}{\nofcharsws18319}{\vern24703}}
\margl1319\margr1319\ltrsect \widowctrl\ftnbj\aenddoc\noxlattoyen\expshrtn\noultrlspc\dntblnsbdb\nospaceforul\formshade\horzdoc\dgmargin\dghspace180\dgvspace180\dghorigin1319\dgvorigin1440\dghshow1\dgvshow1
\jexpand\viewkind1\viewscale100\pgbrdrhead\pgbrdrfoot\splytwnine\ftnlytwnine\htmautsp\nolnhtadjtbl\useltbaln\alntblind\lytcalctblwd\lyttblrtgr\lnbrkrule\nobrkwrptbl\snaptogridincell\allowfieldendsel\wrppunct\asianbrkrule\nojkernpunct\rsidroot2060446 \fet0
\ltrpar \sectd \ltrsect\linex0\endnhere\sectlinegrid360\sectdefaultcl\sectrsid3754175\sftnbj {\*\pnseclvl1\pnucrm\pnqc\pnstart1\pnindent720\pnhang {\pntxta .}}{\*\pnseclvl2\pnucltr\pnqc\pnstart1\pnindent720\pnhang {\pntxta .}}{\*\pnseclvl3
\pndec\pnqc\pnstart1\pnindent720\pnhang {\pntxta .}}{\*\pnseclvl4\pnlcltr\pnqc\pnstart1\pnindent720\pnhang {\pntxta )}}{\*\pnseclvl5\pndec\pnqc\pnstart1\pnindent720\pnhang {\pntxtb (}{\pntxta )}}{\*\pnseclvl6\pnlcltr\pnqc\pnstart1\pnindent720\pnhang 
{\pntxtb (}{\pntxta )}}{\*\pnseclvl7\pnlcrm\pnqc\pnstart1\pnindent720\pnhang {\pntxtb (}{\pntxta )}}{\*\pnseclvl8\pnlcltr\pnqc\pnstart1\pnindent720\pnhang {\pntxtb (}{\pntxta )}}{\*\pnseclvl9\pnlcrm\pnqc\pnstart1\pnindent720\pnhang {\pntxtb (}{\pntxta )}}
\pard\plain \ltrpar\s15\ql \li0\ri0\widctlpar\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid3754175 \rtlch\fcs1 \af2\afs20\alang1025 \ltrch\fcs0 \fs20\lang1033\langfe1028\loch\af2\hich\af2\dbch\af14\cgrid\langnp1033\langfenp1028 {\rtlch\fcs1 
\af2 \ltrch\fcs0 \insrsid6320269\charrsid3754175 \tab \tab \hich\af2\dbch\af14\loch\f2     GNU GENERAL PUBLIC LICENSE
\par \tab \tab \hich\af2\dbch\af14\loch\f2        Version 2, June 1991
\par 
\par \hich\af2\dbch\af14\loch\f2  Copyright (C) 1989, 1991 Free Software Foundation, Inc.
\par \hich\af2\dbch\af14\loch\f2                        51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
\par \hich\af2\dbch\af14\loch\f2  Everyone is permitted to copy and distribute verb\hich\af2\dbch\af14\loch\f2 atim copies
\par \hich\af2\dbch\af14\loch\f2  of this license document, but changing it is not allowed.
\par 
\par \tab \tab \tab \hich\af2\dbch\af14\loch\f2     Preamble
\par 
\par \hich\af2\dbch\af14\loch\f2   The licenses for most software are designed to take away your
\par \hich\af2\dbch\af14\loch\f2 freedom to share and change it.  By contrast, the GNU General Public
\par \hich\af2\dbch\af14\loch\f2 License is intended to guar\hich\af2\dbch\af14\loch\f2 antee your freedom to share and change free
\par \hich\af2\dbch\af14\loch\f2 software--to make sure the software is free for all its users.  This
\par \hich\af2\dbch\af14\loch\f2 General Public License applies to most of the Free Software
\par \hich\af2\dbch\af14\loch\f2 Foundation's software and to any other program whose authors commit to
\par \hich\af2\dbch\af14\loch\f2 using it\hich\af2\dbch\af14\loch\f2 .  (Some other Free Software Foundation software is covered by
\par \hich\af2\dbch\af14\loch\f2 the GNU Library General Public License instead.)  You can apply it to
\par \hich\af2\dbch\af14\loch\f2 your programs, too.
\par 
\par \hich\af2\dbch\af14\loch\f2   When we speak of free software, we are referring to freedom, not
\par \hich\af2\dbch\af14\loch\f2 price.  Our General Public Lic\hich\af2\dbch\af14\loch\f2 enses are designed to make sure that you
\par \hich\af2\dbch\af14\loch\f2 have the freedom to distribute copies of free software (and charge for
\par \hich\af2\dbch\af14\loch\f2 this service if you wish), that you receive source code or can get it
\par \hich\af2\dbch\af14\loch\f2 if you want it, that you can change the software or use pieces of it
\par \hich\af2\dbch\af14\loch\f2 i\hich\af2\dbch\af14\loch\f2 n new free programs; and that you know you can do these things.
\par 
\par \hich\af2\dbch\af14\loch\f2   To protect your rights, we need to make restrictions that forbid
\par \hich\af2\dbch\af14\loch\f2 anyone to deny you these rights or to ask you to surrender the rights.
\par \hich\af2\dbch\af14\loch\f2 These restrictions translate to certain responsib\hich\af2\dbch\af14\loch\f2 ilities for you if you
\par \hich\af2\dbch\af14\loch\f2 distribute copies of the software, or if you modify it.
\par 
\par \hich\af2\dbch\af14\loch\f2   For example, if you distribute copies of such a program, whether
\par \hich\af2\dbch\af14\loch\f2 gratis or for a fee, you must give the recipients all the rights that
\par \hich\af2\dbch\af14\loch\f2 you have.  You must make sure that\hich\af2\dbch\af14\loch\f2  they, too, receive or can get the
\par \hich\af2\dbch\af14\loch\f2 source code.  And you must show them these terms so they know their
\par \hich\af2\dbch\af14\loch\f2 rights.
\par 
\par \hich\af2\dbch\af14\loch\f2   We protect your rights with two steps: (1) copyright the software, and
\par \hich\af2\dbch\af14\loch\f2 (2) offer you this license which gives you legal permission to cop\hich\af2\dbch\af14\loch\f2 y,
\par \hich\af2\dbch\af14\loch\f2 distribute and/or modify the software.
\par 
\par \hich\af2\dbch\af14\loch\f2   Also, for each author's protection and ours, we want to make certain
\par \hich\af2\dbch\af14\loch\f2 that everyone understands that there is no warranty for this free
\par \hich\af2\dbch\af14\loch\f2 software.  If the software is modified by someone else and passed on, w\hich\af2\dbch\af14\loch\f2 e
\par \hich\af2\dbch\af14\loch\f2 want its recipients to know that what they have is not the original, so
\par \hich\af2\dbch\af14\loch\f2 that any problems introduced by others will not reflect on the original
\par \hich\af2\dbch\af14\loch\f2 authors' reputations.
\par 
\par \hich\af2\dbch\af14\loch\f2   Finally, any free program is threatened constantly by software
\par \hich\af2\dbch\af14\loch\f2 patents.  We wis\hich\af2\dbch\af14\loch\f2 h to avoid the danger that redistributors of a free
\par \hich\af2\dbch\af14\loch\f2 program will individually obtain patent licenses, in effect making the
\par \hich\af2\dbch\af14\loch\f2 program proprietary.  To prevent this, we have made it clear that any
\par \hich\af2\dbch\af14\loch\f2 patent must be licensed for everyone's free use or not licen\hich\af2\dbch\af14\loch\f2 sed at all.
\par 
\par \hich\af2\dbch\af14\loch\f2   The precise terms and conditions for copying, distribution and
\par \hich\af2\dbch\af14\loch\f2 modification follow.
\par 
\par 
\par \tab \tab \hich\af2\dbch\af14\loch\f2     GNU GENERAL PUBLIC LICENSE
\par \hich\af2\dbch\af14\loch\f2    TERMS AND CONDITIONS FOR COPYING, DISTRIBUTION AND MODIFICATION
\par 
\par \hich\af2\dbch\af14\loch\f2   0. This License applies to any program or o\hich\af2\dbch\af14\loch\f2 ther work which contains
\par \hich\af2\dbch\af14\loch\f2 a notice placed by the copyright holder saying it may be distributed
\par \hich\af2\dbch\af14\loch\f2 under the terms of this General Public License.  The "Program", below,
\par \hich\af2\dbch\af14\loch\f2 refers to any such program or work, and a "work based on the Program"
\par \hich\af2\dbch\af14\loch\f2 means either the \hich\af2\dbch\af14\loch\f2 Program or any derivative work under copyright law:
\par \hich\af2\dbch\af14\loch\f2 that is to say, a work containing the Program or a portion of it,
\par \hich\af2\dbch\af14\loch\f2 either verbatim or with modifications and/or translated into another
\par \hich\af2\dbch\af14\loch\f2 language.  (Hereinafter, translation is included without limitatio\hich\af2\dbch\af14\loch\f2 n in
\par \hich\af2\dbch\af14\loch\f2 the term "modification".)  Each licensee is addressed as "you".
\par 
\par \hich\af2\dbch\af14\loch\f2 Activities other than copying, distribution and modification are not
\par \hich\af2\dbch\af14\loch\f2 covered by this License; they are outside its scope.  The act of
\par \hich\af2\dbch\af14\loch\f2 running the Program is not restricted, and the \hich\af2\dbch\af14\loch\f2 output from the Program
\par \hich\af2\dbch\af14\loch\f2 is covered only if its contents constitute a work based on the
\par \hich\af2\dbch\af14\loch\f2 Program (independent of having been made by running the Program).
\par \hich\af2\dbch\af14\loch\f2 Whether that is true depends on what the Program does.
\par 
\par \hich\af2\dbch\af14\loch\f2   1. You may copy and distribute verbatim \hich\af2\dbch\af14\loch\f2 copies of the Program's
\par \hich\af2\dbch\af14\loch\f2 source code as you receive it, in any medium, provided that you
\par \hich\af2\dbch\af14\loch\f2 conspicuously and appropriately publish on each copy an appropriate
\par \hich\af2\dbch\af14\loch\f2 copyright notice and disclaimer of warranty; keep intact all the
\par \hich\af2\dbch\af14\loch\f2 notices that refer to this Lice\hich\af2\dbch\af14\loch\f2 nse and to the absence of any warranty;
\par \hich\af2\dbch\af14\loch\f2 and give any other recipients of the Program a copy of this License
\par \hich\af2\dbch\af14\loch\f2 along with the Program.
\par 
\par \hich\af2\dbch\af14\loch\f2 You may charge a fee for the physical act of transferring a copy, and
\par \hich\af2\dbch\af14\loch\f2 you may at your option offer warranty protection\hich\af2\dbch\af14\loch\f2  in exchange for a fee.
\par 
\par \hich\af2\dbch\af14\loch\f2   2. You may modify your copy or copies of the Program or any portion
\par \hich\af2\dbch\af14\loch\f2 of it, thus forming a work based on the Program, and copy and
\par \hich\af2\dbch\af14\loch\f2 distribute such modifications or work under the terms of Section 1
\par \hich\af2\dbch\af14\loch\f2 above, provided that you al\hich\af2\dbch\af14\loch\f2 so meet all of these conditions:
\par 
\par \hich\af2\dbch\af14\loch\f2     a) You must cause the modified files to carry prominent notices
\par \hich\af2\dbch\af14\loch\f2     stating that you changed the files and the date of any change.
\par 
\par \hich\af2\dbch\af14\loch\f2     b) You must cause any work that you distribute or publish, that in
\par \hich\af2\dbch\af14\loch\f2     whole\hich\af2\dbch\af14\loch\f2  or in part contains or is derived from the Program or any
\par \hich\af2\dbch\af14\loch\f2     part thereof, to be licensed as a whole at no charge to all third
\par \hich\af2\dbch\af14\loch\f2     parties under the terms of this License.
\par 
\par \hich\af2\dbch\af14\loch\f2     c) If the modified program normally reads commands interactively
\par \hich\af2\dbch\af14\loch\f2     whe\hich\af2\dbch\af14\loch\f2 n run, you must cause it, when started running for such
\par \hich\af2\dbch\af14\loch\f2     interactive use in the most ordinary way, to print or display an
\par \hich\af2\dbch\af14\loch\f2     announcement including an appropriate copyright notice and a
\par \hich\af2\dbch\af14\loch\f2     notice that there is no warranty (or else, saying that you \hich\af2\dbch\af14\loch\f2 provide
\par \hich\af2\dbch\af14\loch\f2     a warranty) and that users may redistribute the program under
\par \hich\af2\dbch\af14\loch\f2     these conditions, and telling the user how to view a copy of this
\par \hich\af2\dbch\af14\loch\f2     License.  (Exception: if the Program itself is interactive but
\par \hich\af2\dbch\af14\loch\f2     does not normally print such an annou\hich\af2\dbch\af14\loch\f2 ncement, your work based on
\par \hich\af2\dbch\af14\loch\f2     the Program is not required to print an announcement.)
\par 
\par 
\par \hich\af2\dbch\af14\loch\f2 These requirements apply to the modified work as a whole.  If
\par \hich\af2\dbch\af14\loch\f2 identifiable sections of that work are not derived from the Program,
\par \hich\af2\dbch\af14\loch\f2 and can be reasonably consider\hich\af2\dbch\af14\loch\f2 ed independent and separate works in
\par \hich\af2\dbch\af14\loch\f2 themselves, then this License, and its terms, do not apply to those
\par \hich\af2\dbch\af14\loch\f2 sections when you distribute them as separate works.  But when you
\par \hich\af2\dbch\af14\loch\f2 distribute the same sections as part of a whole which is a work based
\par \hich\af2\dbch\af14\loch\f2 on the Pro\hich\af2\dbch\af14\loch\f2 gram, the distribution of the whole must be on the terms of
\par \hich\af2\dbch\af14\loch\f2 this License, whose permissions for other licensees extend to the
\par \hich\af2\dbch\af14\loch\f2 entire whole, and thus to each and every part regardless of who wrote it.
\par 
\par \hich\af2\dbch\af14\loch\f2 Thus, it is not the intent of this section to claim\hich\af2\dbch\af14\loch\f2  rights or contest
\par \hich\af2\dbch\af14\loch\f2 your rights to work written entirely by you; rather, the intent is to
\par \hich\af2\dbch\af14\loch\f2 exercise the right to control the distribution of derivative or
\par \hich\af2\dbch\af14\loch\f2 collective works based on the Program.
\par 
\par \hich\af2\dbch\af14\loch\f2 In addition, mere aggregation of another work not based on\hich\af2\dbch\af14\loch\f2  the Program
\par \hich\af2\dbch\af14\loch\f2 with the Program (or with a work based on the Program) on a volume of
\par \hich\af2\dbch\af14\loch\f2 a storage or distribution medium does not bring the other work under
\par \hich\af2\dbch\af14\loch\f2 the scope of this License.
\par 
\par \hich\af2\dbch\af14\loch\f2   3. You may copy and distribute the Program (or a work based on it,
\par \hich\af2\dbch\af14\loch\f2 u\hich\af2\dbch\af14\loch\f2 nder Section 2) in object code or executable form under the terms of
\par \hich\af2\dbch\af14\loch\f2 Sections 1 and 2 above provided that you also do one of the following:
\par 
\par \hich\af2\dbch\af14\loch\f2     a) Accompany it with the complete corresponding machine-readable
\par \hich\af2\dbch\af14\loch\f2     source code, which must be distributed\hich\af2\dbch\af14\loch\f2  under the terms of Sections
\par \hich\af2\dbch\af14\loch\f2     1 and 2 above on a medium customarily used for software interchange; or,
\par 
\par \hich\af2\dbch\af14\loch\f2     b) Accompany it with a written offer, valid for at least three
\par \hich\af2\dbch\af14\loch\f2     years, to give any third party, for a charge no more than your
\par \hich\af2\dbch\af14\loch\f2     cost o\hich\af2\dbch\af14\loch\f2 f physically performing source distribution, a complete
\par \hich\af2\dbch\af14\loch\f2     machine-readable copy of the corresponding source code, to be
\par \hich\af2\dbch\af14\loch\f2     distributed under the terms of Sections 1 and 2 above on a medium
\par \hich\af2\dbch\af14\loch\f2     customarily used for software interchange; or,
\par 
\par \hich\af2\dbch\af14\loch\f2     c) \hich\af2\dbch\af14\loch\f2 Accompany it with the information you received as to the offer
\par \hich\af2\dbch\af14\loch\f2     to distribute corresponding source code.  (This alternative is
\par \hich\af2\dbch\af14\loch\f2     allowed only for noncommercial distribution and only if you
\par \hich\af2\dbch\af14\loch\f2     received the program in object code or executable form \hich\af2\dbch\af14\loch\f2 with such
\par \hich\af2\dbch\af14\loch\f2     an offer, in accord with Subsection b above.)
\par 
\par \hich\af2\dbch\af14\loch\f2 The source code for a work means the preferred form of the work for
\par \hich\af2\dbch\af14\loch\f2 making modifications to it.  For an executable work, complete source
\par \hich\af2\dbch\af14\loch\f2 code means all the source code for all modules it con\hich\af2\dbch\af14\loch\f2 tains, plus any
\par \hich\af2\dbch\af14\loch\f2 associated interface definition files, plus the scripts used to
\par \hich\af2\dbch\af14\loch\f2 control compilation and installation of the executable.  However, as a
\par \hich\af2\dbch\af14\loch\f2 special exception, the source code distributed need not include
\par \hich\af2\dbch\af14\loch\f2 anything that is normally distributed\hich\af2\dbch\af14\loch\f2  (in either source or binary
\par \hich\af2\dbch\af14\loch\f2 form) with the major components (compiler, kernel, and so on) of the
\par \hich\af2\dbch\af14\loch\f2 operating system on which the executable runs, unless that component
\par \hich\af2\dbch\af14\loch\f2 itself accompanies the executable.
\par 
\par \hich\af2\dbch\af14\loch\f2 If distribution of executable or object code is \hich\af2\dbch\af14\loch\f2 made by offering
\par \hich\af2\dbch\af14\loch\f2 access to copy from a designated place, then offering equivalent
\par \hich\af2\dbch\af14\loch\f2 access to copy the source code from the same place counts as
\par \hich\af2\dbch\af14\loch\f2 distribution of the source code, even though third parties are not
\par \hich\af2\dbch\af14\loch\f2 compelled to copy the source along with th\hich\af2\dbch\af14\loch\f2 e object code.
\par 
\par 
\par \hich\af2\dbch\af14\loch\f2   4. You may not copy, modify, sublicense, or distribute the Program
\par \hich\af2\dbch\af14\loch\f2 except as expressly provided under this License.  Any attempt
\par \hich\af2\dbch\af14\loch\f2 otherwise to copy, modify, sublicense or distribute the Program is
\par \hich\af2\dbch\af14\loch\f2 void, and will automatically termin\hich\af2\dbch\af14\loch\f2 ate your rights under this License.
\par \hich\af2\dbch\af14\loch\f2 However, parties who have received copies, or rights, from you under
\par \hich\af2\dbch\af14\loch\f2 this License will not have their licenses terminated so long as such
\par \hich\af2\dbch\af14\loch\f2 parties remain in full compliance.
\par 
\par \hich\af2\dbch\af14\loch\f2   5. You are not required to accept this \hich\af2\dbch\af14\loch\f2 License, since you have not
\par \hich\af2\dbch\af14\loch\f2 signed it.  However, nothing else grants you permission to modify or
\par \hich\af2\dbch\af14\loch\f2 distribute the Program or its derivative works.  These actions are
\par \hich\af2\dbch\af14\loch\f2 prohibited by law if you do not accept this License.  Therefore, by
\par \hich\af2\dbch\af14\loch\f2 modifying or distrib\hich\af2\dbch\af14\loch\f2 uting the Program (or any work based on the
\par \hich\af2\dbch\af14\loch\f2 Program), you indicate your acceptance of this License to do so, and
\par \hich\af2\dbch\af14\loch\f2 all its terms and conditions for copying, distributing or modifying
\par \hich\af2\dbch\af14\loch\f2 the Program or works based on it.
\par 
\par \hich\af2\dbch\af14\loch\f2   6. Each time you redistribute the\hich\af2\dbch\af14\loch\f2  Program (or any work based on the
\par \hich\af2\dbch\af14\loch\f2 Program), the recipient automatically receives a license from the
\par \hich\af2\dbch\af14\loch\f2 original licensor to copy, distribute or modify the Program subject to
\par \hich\af2\dbch\af14\loch\f2 these terms and conditions.  You may not impose any further
\par \hich\af2\dbch\af14\loch\f2 restrictions on the \hich\af2\dbch\af14\loch\f2 recipients' exercise of the rights granted herein.
\par \hich\af2\dbch\af14\loch\f2 You are not responsible for enforcing compliance by third parties to
\par \hich\af2\dbch\af14\loch\f2 this License.
\par 
\par \hich\af2\dbch\af14\loch\f2   7. If, as a consequence of a court judgment or allegation of patent
\par \hich\af2\dbch\af14\loch\f2 infringement or for any other reason (not limi\hich\af2\dbch\af14\loch\f2 ted to patent issues),
\par \hich\af2\dbch\af14\loch\f2 conditions are imposed on you (whether by court order, agreement or
\par \hich\af2\dbch\af14\loch\f2 otherwise) that contradict the conditions of this License, they do not
\par \hich\af2\dbch\af14\loch\f2 excuse you from the conditions of this License.  If you cannot
\par \hich\af2\dbch\af14\loch\f2 distribute so as to satisfy\hich\af2\dbch\af14\loch\f2  simultaneously your obligations under this
\par \hich\af2\dbch\af14\loch\f2 License and any other pertinent obligations, then as a consequence you
\par \hich\af2\dbch\af14\loch\f2 may not distribute the Program at all.  For example, if a patent
\par \hich\af2\dbch\af14\loch\f2 license would not permit royalty-free redistribution of the Program by
\par \hich\af2\dbch\af14\loch\f2 a\hich\af2\dbch\af14\loch\f2 ll those who receive copies directly or indirectly through you, then
\par \hich\af2\dbch\af14\loch\f2 the only way you could satisfy both it and this License would be to
\par \hich\af2\dbch\af14\loch\f2 refrain entirely from distribution of the Program.
\par 
\par \hich\af2\dbch\af14\loch\f2 If any portion of this section is held invalid or unenforceable\hich\af2\dbch\af14\loch\f2  under
\par \hich\af2\dbch\af14\loch\f2 any particular circumstance, the balance of the section is intended to
\par \hich\af2\dbch\af14\loch\f2 apply and the section as a whole is intended to apply in other
\par \hich\af2\dbch\af14\loch\f2 circumstances.
\par 
\par \hich\af2\dbch\af14\loch\f2 It is not the purpose of this section to induce you to infringe any
\par \hich\af2\dbch\af14\loch\f2 patents or other property\hich\af2\dbch\af14\loch\f2  right claims or to contest validity of any
\par \hich\af2\dbch\af14\loch\f2 such claims; this section has the sole purpose of protecting the
\par \hich\af2\dbch\af14\loch\f2 integrity of the free software distribution system, which is
\par \hich\af2\dbch\af14\loch\f2 implemented by public license practices.  Many people have made
\par \hich\af2\dbch\af14\loch\f2 generous contribut\hich\af2\dbch\af14\loch\f2 ions to the wide range of software distributed
\par \hich\af2\dbch\af14\loch\f2 through that system in reliance on consistent application of that
\par \hich\af2\dbch\af14\loch\f2 system; it is up to the author/donor to decide if he or she is willing
\par \hich\af2\dbch\af14\loch\f2 to distribute software through any other system and a licensee cannot
\par \hich\af2\dbch\af14\loch\f2 impose that choice.
\par 
\par \hich\af2\dbch\af14\loch\f2 This section is intended to make thoroughly clear what is believed to
\par \hich\af2\dbch\af14\loch\f2 be a consequence of the rest of this License.
\par 
\par 
\par \hich\af2\dbch\af14\loch\f2   8. If the distribution and/or use of the Program is restricted in
\par \hich\af2\dbch\af14\loch\f2 certain countries either by patents or b\hich\af2\dbch\af14\loch\f2 y copyrighted interfaces, the
\par \hich\af2\dbch\af14\loch\f2 original copyright holder who places the Program under this License
\par \hich\af2\dbch\af14\loch\f2 may add an explicit geographical distribution limitation excluding
\par \hich\af2\dbch\af14\loch\f2 those countries, so that distribution is permitted only in or among
\par \hich\af2\dbch\af14\loch\f2 countries not thus \hich\af2\dbch\af14\loch\f2 excluded.  In such case, this License incorporates
\par \hich\af2\dbch\af14\loch\f2 the limitation as if written in the body of this License.
\par 
\par \hich\af2\dbch\af14\loch\f2   9. The Free Software Foundation may publish revised and/or new versions
\par \hich\af2\dbch\af14\loch\f2 of the General Public License from time to time.  Such new versions \hich\af2\dbch\af14\loch\f2 will
\par \hich\af2\dbch\af14\loch\f2 be similar in spirit to the present version, but may differ in detail to
\par \hich\af2\dbch\af14\loch\f2 address new problems or concerns.
\par 
\par \hich\af2\dbch\af14\loch\f2 Each version is given a distinguishing version number.  If the Program
\par \hich\af2\dbch\af14\loch\f2 specifies a version number of this License which applies to it and \hich\af2\dbch\af14\loch\f2 "any
\par \hich\af2\dbch\af14\loch\f2 later version", you have the option of following the terms and conditions
\par \hich\af2\dbch\af14\loch\f2 either of that version or of any later version published by the Free
\par \hich\af2\dbch\af14\loch\f2 Software Foundation.  If the Program does not specify a version number of
\par \hich\af2\dbch\af14\loch\f2 this License, you may choose a\hich\af2\dbch\af14\loch\f2 ny version ever published by the Free Software
\par \hich\af2\dbch\af14\loch\f2 Foundation.
\par 
\par \hich\af2\dbch\af14\loch\f2   10. If you wish to incorporate parts of the Program into other free
\par \hich\af2\dbch\af14\loch\f2 programs whose distribution conditions are different, write to the author
\par \hich\af2\dbch\af14\loch\f2 to ask for permission.  For software which is c\hich\af2\dbch\af14\loch\f2 opyrighted by the Free
\par \hich\af2\dbch\af14\loch\f2 Software Foundation, write to the Free Software Foundation; we sometimes
\par \hich\af2\dbch\af14\loch\f2 make exceptions for this.  Our decision will be guided by the two goals
\par \hich\af2\dbch\af14\loch\f2 of preserving the free status of all derivatives of our free software and
\par \hich\af2\dbch\af14\loch\f2 of promoti\hich\af2\dbch\af14\loch\f2 ng the sharing and reuse of software generally.
\par 
\par \tab \tab \tab \hich\af2\dbch\af14\loch\f2     NO WARRANTY
\par 
\par \hich\af2\dbch\af14\loch\f2   11. BECAUSE THE PROGRAM IS LICENSED FREE OF CHARGE, THERE IS NO WARRANTY
\par \hich\af2\dbch\af14\loch\f2 FOR THE PROGRAM, TO THE EXTENT PERMITTED BY APPLICABLE LAW.  EXCEPT WHEN
\par \hich\af2\dbch\af14\loch\f2 OTHERWISE STATED IN WRITING THE C\hich\af2\dbch\af14\loch\f2 OPYRIGHT HOLDERS AND/OR OTHER PARTIES
\par \hich\af2\dbch\af14\loch\f2 PROVIDE THE PROGRAM "AS IS" WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESSED
\par \hich\af2\dbch\af14\loch\f2 OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
\par \hich\af2\dbch\af14\loch\f2 MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.  THE ENTIRE RISK \hich\af2\dbch\af14\loch\f2 AS
\par \hich\af2\dbch\af14\loch\f2 TO THE QUALITY AND PERFORMANCE OF THE PROGRAM IS WITH YOU.  SHOULD THE
\par \hich\af2\dbch\af14\loch\f2 PROGRAM PROVE DEFECTIVE, YOU ASSUME THE COST OF ALL NECESSARY SERVICING,
\par \hich\af2\dbch\af14\loch\f2 REPAIR OR CORRECTION.
\par 
\par \hich\af2\dbch\af14\loch\f2   12. IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN WRITING
\par \hich\af2\dbch\af14\loch\f2 WILL\hich\af2\dbch\af14\loch\f2  ANY COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MAY MODIFY AND/OR
\par \hich\af2\dbch\af14\loch\f2 REDISTRIBUTE THE PROGRAM AS PERMITTED ABOVE, BE LIABLE TO YOU FOR DAMAGES,
\par \hich\af2\dbch\af14\loch\f2 INCLUDING ANY GENERAL, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES ARISING
\par \hich\af2\dbch\af14\loch\f2 OUT OF THE USE OR INABILITY TO USE THE\hich\af2\dbch\af14\loch\f2  PROGRAM (INCLUDING BUT NOT LIMITED
\par \hich\af2\dbch\af14\loch\f2 TO LOSS OF DATA OR DATA BEING RENDERED INACCURATE OR LOSSES SUSTAINED BY
\par \hich\af2\dbch\af14\loch\f2 YOU OR THIRD PARTIES OR A FAILURE OF THE PROGRAM TO OPERATE WITH ANY OTHER
\par \hich\af2\dbch\af14\loch\f2 PROGRAMS), EVEN IF SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE
\par \hich\af2\dbch\af14\loch\f2 POSSIBILITY OF SUCH DAMAGES.
\par 
\par \tab \tab \hich\af2\dbch\af14\loch\f2      END OF TERMS AND CONDITIONS
\par 
\par 
\par \tab \hich\af2\dbch\af14\loch\f2     How to Apply These Terms to Your New Programs
\par 
\par \hich\af2\dbch\af14\loch\f2   If you develop a new program, and you want it to be of the greatest
\par \hich\af2\dbch\af14\loch\f2 possible use to the public, the best way to achieve this \hich\af2\dbch\af14\loch\f2 is to make it
\par \hich\af2\dbch\af14\loch\f2 free software which everyone can redistribute and change under these terms.
\par 
\par \hich\af2\dbch\af14\loch\f2   To do so, attach the following notices to the program.  It is safest
\par \hich\af2\dbch\af14\loch\f2 to attach them to the start of each source file to most effectively
\par \hich\af2\dbch\af14\loch\f2 convey the exclusion \hich\af2\dbch\af14\loch\f2 of warranty; and each file should have at least
\par \hich\af2\dbch\af14\loch\f2 the "copyright" line and a pointer to where the full notice is found.
\par 
\par \hich\af2\dbch\af14\loch\f2     <one line to give the program's name and a brief idea of what it does.>
\par \hich\af2\dbch\af14\loch\f2     Copyright (C) <year>  <name of author>
\par 
\par \hich\af2\dbch\af14\loch\f2     This pr\hich\af2\dbch\af14\loch\f2 ogram is free software; you can redistribute it and/or modify
\par \hich\af2\dbch\af14\loch\f2     it under the terms of the GNU General Public License as published by
\par \hich\af2\dbch\af14\loch\f2     the Free Software Foundation; either version 2 of the License, or
\par \hich\af2\dbch\af14\loch\f2     (at your option) any later version.
\par 
\par \hich\af2\dbch\af14\loch\f2     T\hich\af2\dbch\af14\loch\f2 his program is distributed in the hope that it will be useful,
\par \hich\af2\dbch\af14\loch\f2     but WITHOUT ANY WARRANTY; without even the implied warranty of
\par \hich\af2\dbch\af14\loch\f2     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
\par \hich\af2\dbch\af14\loch\f2     GNU General Public License for more details.
\par 
\par \hich\af2\dbch\af14\loch\f2     Y\hich\af2\dbch\af14\loch\f2 ou should have received a copy of the GNU General Public License
\par \hich\af2\dbch\af14\loch\f2     along with this program; if not, write to the Free Software
\par \hich\af2\dbch\af14\loch\f2     Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
\par 
\par 
\par \hich\af2\dbch\af14\loch\f2 Also add information on how to contact yo\hich\af2\dbch\af14\loch\f2 u by electronic and paper mail.
\par 
\par \hich\af2\dbch\af14\loch\f2 If the program is interactive, make it output a short notice like this
\par \hich\af2\dbch\af14\loch\f2 when it starts in an interactive mode:
\par 
\par \hich\af2\dbch\af14\loch\f2     Gnomovision version 69, Copyright (C) year name of author
\par \hich\af2\dbch\af14\loch\f2     Gnomovision comes with ABSOLUTELY NO WAR\hich\af2\dbch\af14\loch\f2 RANTY; for details type `show w'.
\par \hich\af2\dbch\af14\loch\f2     This is free software, and you are welcome to redistribute it
\par \hich\af2\dbch\af14\loch\f2     under certain conditions; type `show c' for details.
\par 
\par \hich\af2\dbch\af14\loch\f2 The hypothetical commands `show w' and `show c' should show the appropriate
\par \hich\af2\dbch\af14\loch\f2 parts of the Gene\hich\af2\dbch\af14\loch\f2 ral Public License.  Of course, the commands you use may
\par \hich\af2\dbch\af14\loch\f2 be called something other than `show w' and `show c'; they could even be
\par \hich\af2\dbch\af14\loch\f2 mouse-clicks or menu items--whatever suits your program.
\par 
\par \hich\af2\dbch\af14\loch\f2 You should also get your employer (if you work as a programmer) \hich\af2\dbch\af14\loch\f2 or your
\par \hich\af2\dbch\af14\loch\f2 school, if any, to sign a "copyright disclaimer" for the program, if
\par \hich\af2\dbch\af14\loch\f2 necessary.  Here is a sample; alter the names:
\par 
\par \hich\af2\dbch\af14\loch\f2   Yoyodyne, Inc., hereby disclaims all copyright interest in the program
\par \hich\af2\dbch\af14\loch\f2   `Gnomovision' (which makes passes at compilers) wri\hich\af2\dbch\af14\loch\f2 tten by James Hacker.
\par 
\par \hich\af2\dbch\af14\loch\f2   <signature of Ty Coon>, 1 April 1989
\par \hich\af2\dbch\af14\loch\f2   Ty Coon, President of Vice
\par 
\par \hich\af2\dbch\af14\loch\f2 This General Public License does not permit incorporating your program into
\par \hich\af2\dbch\af14\loch\f2 proprietary programs.  If your program is a subroutine library, you may
\par \hich\af2\dbch\af14\loch\f2 consider \hich\af2\dbch\af14\loch\f2 it more useful to permit linking proprietary applications with the
\par \hich\af2\dbch\af14\loch\f2 library.  If this is what you want to do, use the GNU Library General
\par \hich\af2\dbch\af14\loch\f2 Public License instead of this License\hich\af2\dbch\af14\loch\f2 .}{\rtlch\fcs1 \af2 \ltrch\fcs0 \insrsid3754175 
\par }}