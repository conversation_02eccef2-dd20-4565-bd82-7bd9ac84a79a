{\rtf1\ansi\ansicpg1252\cocoartf1504\cocoasubrtf830
{\fonttbl\f0\fswiss\fcharset0 ArialMT;\f1\fnil\fcharset0 HelveticaNeue;\f2\fmodern\fcharset0 CourierNewPSMT;
}
{\colortbl;\red255\green255\blue255;\red53\green53\blue53;}
{\*\expandedcolortbl;;\cssrgb\c27059\c27059\c27059;}
{\*\listtable{\list\listtemplateid1\listhybrid{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace360\levelindent0{\*\levelmarker \{decimal\}.}{\leveltext\leveltemplateid1\'02\'00.;}{\levelnumbers\'01;}\fi-360\li720\lin720 }{\listlevel\levelnfc4\levelnfcn4\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace360\levelindent0{\*\levelmarker \{lower-alpha\}.}{\leveltext\leveltemplateid2\'02\'01.;}{\levelnumbers\'01;}\fi-360\li1440\lin1440 }{\listname ;}\listid1}
{\list\listtemplateid2\listhybrid{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace360\levelindent0{\*\levelmarker \{decimal\}.}{\leveltext\leveltemplateid101\'02\'00.;}{\levelnumbers\'01;}\fi-360\li720\lin720 }{\listlevel\levelnfc4\levelnfcn4\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace360\levelindent0{\*\levelmarker \{lower-alpha\}.}{\leveltext\leveltemplateid102\'02\'01.;}{\levelnumbers\'01;}\fi-360\li1440\lin1440 }{\listname ;}\listid2}
{\list\listtemplateid3\listhybrid{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace360\levelindent0{\*\levelmarker \{decimal\}.}{\leveltext\leveltemplateid201\'02\'00.;}{\levelnumbers\'01;}\fi-360\li720\lin720 }{\listlevel\levelnfc4\levelnfcn4\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace360\levelindent0{\*\levelmarker \{lower-alpha\}.}{\leveltext\leveltemplateid202\'02\'01.;}{\levelnumbers\'01;}\fi-360\li1440\lin1440 }{\listname ;}\listid3}
{\list\listtemplateid4\listhybrid{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace360\levelindent0{\*\levelmarker \{decimal\}.}{\leveltext\leveltemplateid301\'02\'00.;}{\levelnumbers\'01;}\fi-360\li720\lin720 }{\listlevel\levelnfc4\levelnfcn4\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace360\levelindent0{\*\levelmarker \{lower-alpha\}.}{\leveltext\leveltemplateid302\'02\'01.;}{\levelnumbers\'01;}\fi-360\li1440\lin1440 }{\listname ;}\listid4}}
{\*\listoverridetable{\listoverride\listid1\listoverridecount0\ls1}{\listoverride\listid2\listoverridecount0\ls2}{\listoverride\listid3\listoverridecount0\ls3}{\listoverride\listid4\listoverridecount0\ls4}}
{\info
{\author Nathan Salomonis}
{\*\company CCHMC}}\margl1440\margr1440\vieww12240\viewh15840\viewkind1\viewscale97
\deftab720
\pard\pardeftab720\ri0\partightenfactor0

\f0\b\fs24 \cf0 Input Files for Kallisto-Splice Analysis\
\pard\pardeftab720\ri0\partightenfactor0

\b0 \cf0 \
The latest version of AltAnalyze introduces a new method to quickly process raw sequencing data (FASTQ files) to directly produce gene expression and alternative splicing estimates, without any additional software on your desktop or laptop computer.\
\
\pard\pardeftab720\ri0\partightenfactor0

\b \cf0 Demo Files
\b0 \
Two zip files with very small FASTQ files for demonstration purposes are available here:\
http://altanalyze.org/Data/Hs_GSE45419_FASTQs.zip (344MB - Human downsampled)\
http://altanalyze.org/Data/Mm-FASTQ-GSE70245.zip (59MB - Mouse scRNA-Seq)\
\
The Breast Cancer dataset was downsampled from the original fast files as described here: https://www.synapse.org/#!Synapse:syn7286377/files/\
The human breast cancer samples correspond to two subtypes (ER-positive and Triple-negative). Unzip these file before proceeding (right click and extract of open and extract to this directory - e.g., WinZip). \
\pard\pardeftab720\li1440\ri0\partightenfactor0
\cf0 \
\pard\pardeftab720\ri0\partightenfactor0

\b \cf0 Running Kallisto-Splice through the Graphical User Interface\
\
\pard\pardeftab720\li720\fi-360\ri0\partightenfactor0
\ls1\ilvl1
\b0 \cf0 1.	Double-click on the AltAnalyze executable (see Running-AltAnalyze file in the program directory for problems opening). \
2.	Install the correct species database when prompted.\
\pard\pardeftab720\li1440\fi-360\ri0\partightenfactor0
\ls1\ilvl1\cf0 a.	For the demo dataset, ensure Homo Sapiens is downloaded. Any version of the database should be compatible (e.g., EnsMart72). \
\pard\pardeftab720\li720\fi-360\ri0\partightenfactor0
\ls1\ilvl1\cf0 3.	From the main menu in AltAnalyze, select RNA-Seq as the \'93Select vendor/data type\'94. Then select the Continue button.\
4.	Select the \'93Process RNA-Seq reads\'94 radio button. Then select the Continue button.\
5.	
\b \ul Dataset Location
\b0 \ulnone : Enter a dataset name of choice (e.g., Breast_cancer). For the \'93Select FASTQ files to run in Kallisto\'94 , select the location of the unzipped FASTQ directory. The program will process all FASTQ files in that directory. Select the output directory, which is the folder to save all results and subsequent input files to. Then select the Continue button.\
\pard\pardeftab720\li720\fi-360\ri0\partightenfactor0
\ls1\ilvl1
\b \cf0 6.	\ul Expression Analysis Parameters
\b0 \ulnone : Choose the additional options you want to include or exclude for the pipeline analyses (optional). These include pathway analyses options, which statistical comparison tests to apply for differential expression analysis. If users wish they can select \'93no\'94 for the option \'93Perform alternative analysis, which will skip the splicing analyses and process the Kallisto TPM expression file instead of the produced exon-exon junction derived gene RPKM file. When complete, select the Continue button. \
\ls1\ilvl1
\b 7.	\ul Pathway Analysis Options
\b0 \ulnone : Here, the user will be prompted to specify the statistical cutoff applied for differential gene and splicing analyses. The adjp indicates an FDR corrected p-value versus a non-corrected p-value. \
\ls1\ilvl1
\b 8.	\ul Alternative Splicing Analysis Options
\b0 \ulnone : The default recommended method for splicing analysis is selected (MultiPath-PSI), however, alternative and additional algorithm options are available. When finished, select the \'93Run Analysis\'94 option.\
\ls1\ilvl1
\b 9.	\ul Groups Designation
\b0 \ulnone : Type a label for each FASTQ sample shown (e.g., ERpos, TripleNeg). This will create a \'93groups.\'94 text file in the output directory folder ExpressionInput. This file will be reloaded when. \ul \
\pard\pardeftab720\li720\fi-360\ri0\partightenfactor0
\cf0 \ulnone \
\
\pard\pardeftab720\li720\fi-360\ri0\partightenfactor0
\ls2\ilvl1
\b \cf0 10.	\ul Comparisons Designation
\b0 \ulnone : Select the experimental and control datasets to compare to (e.g., TripleNeg vs. ERpos). Select \'93Continue\'94 to run the analysis.\
\ls2\ilvl1
\b 11.	\ul Analysis Progress
\b0 \ulnone : A black screen will appear once the analysis has begun. Be patient as the software is performing a series of in-depth analyses, including indexing of the Kallisto transcriptome (run the first time FASTQ files are processed), Kallisto pseudo-alignment to the reference transcriptome, BAM file generation with genome coordinates for all pseudo-aligned reads, gene expression quantification, differential gene expression analysis, QC analysis, network analysis, marker identification, pathway analysis and alternative splicing analysis.
\b \
\pard\pardeftab720\ri0\partightenfactor0

\b0 \cf0 \
\pard\pardeftab720\ri0\partightenfactor0

\b \cf0 Outputs of Kallisto-Splice\
\pard\pardeftab720\ri0\partightenfactor0

\b0 \cf0 \
There are a large array of results from this workflow which can be found in the below described folders. Note, a separate PDF file is saved to the root directory describing the files in each of these folders. Please refer to those PDFs for details.\
\pard\pardeftab720\li720\fi-360\ri0\partightenfactor0
\ls3\ilvl1
\b \cf0 1.	\ul ExpressionInput:
\b0 \ulnone  This includes all expression estimates for exon-exon junctions, kallisto isofroms and genes as normalized values (TPM and RPKM) and counts. All Kallisto results are saved to the Kallisto_results folder along with the number of percentage of aligned reads.\
\ls3\ilvl1
\b 2.	\ul ExpressionOutput:
\b0 \ulnone  This folder contains all computed differential gene expression results, primarily found in the DATASET file. The MarkerFinder folder contains the top markers assigned to each sample group (AllGenes_correlations-ReplicateBased.txt file).\
\ls3\ilvl1
\b 3.	\ul DataPlots
\b0 \ulnone : This folder contains the majority of saved plots as pdf and png files. Note, that the MarkerFinder folder in this directory contains additional plots. Splicing associated plots will also be saved to the folder AltResults/AlternativeOutput.\
\ls3\ilvl1
\b 5.	\ul AltResults
\b0 \ulnone : This directory contains all splicing analysis results. This most important file is \'93Hs_RNASeq_top_alt_junctions-PSI_EventAnnotation.txt\'94, which contains all MultiPath-PSI detected splicing events and associated annotations. Statistical comparison results are saved to the \'93Events-dPSI\'94 folder and splicing graphs to the SashimiPlots folder in the output directory (derived from the BAM files in the output directory.\
\pard\pardeftab720\li720\fi-360\ri0\partightenfactor0
\ls4\ilvl1
\b \cf0 3.	\ul SashimiPlots
\b0 \ulnone : This folder contains the PDF and PNG outputs for genome and exon-exon junction aligned reads associated with example top-significant alternative splicing events. Users can output addition such plots from the Additional Analyses menu option \'93AltExon Viewer\'94.\
\ls4\ilvl1
\b 5.	\ul GO-Elite
\b0 \ulnone : This folder contains all pathway and gene-set enrichment analysis results. See each folder for the \'93pruned-results_z-score_elite.txt\'94 (open in Excel or equivalent). Network graphs, heatmaps comparing different comparison groups and optional colored WikiPathways are saved to these directories. \
\pard\pardeftab720\ri0\partightenfactor0
\cf0 \
\
Below is an example command similar to the above analysis:\
\pard\pardeftab560\slleading20\partightenfactor0

\f1 \cf2 \

\f2 python AltAnalyze.py --platform "RNASeq" --species Mm \'97fastq_dir /Users/<USER>/DemoData/Mm-FASTQ-GSE70245-DownSampled/ --groupdir /Users/<USER>/DemoData/Mm-FASTQ-GSE70245-DownSampled/groups.Breast_cancer.txt --compdir /Users/<USER>/DemoData/Mm-FASTQ-GSE70245-DownSampled/comp.Breast_cancer.txt --output /Users/<USER>/DemoData/Mm-FASTQ-GSE70245-DownSampled/output --expname Breast_cancer --runGOElite yes --returnPathways all\
\
}