{\rtf1\ansi\ansicpg1252\cocoartf1504\cocoasubrtf830
{\fonttbl\f0\fswiss\fcharset0 ArialMT;\f1\fnil\fcharset0 HelveticaNeue;\f2\fmodern\fcharset0 CourierNewPSMT;
}
{\colortbl;\red255\green255\blue255;\red53\green53\blue53;}
{\*\expandedcolortbl;;\cssrgb\c27059\c27059\c27059;}
{\*\listtable{\list\listtemplateid1\listhybrid{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace360\levelindent0{\*\levelmarker \{decimal\}.}{\leveltext\leveltemplateid1\'02\'00.;}{\levelnumbers\'01;}\fi-360\li720\lin720 }{\listlevel\levelnfc4\levelnfcn4\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace360\levelindent0{\*\levelmarker \{lower-alpha\}.}{\leveltext\leveltemplateid2\'02\'01.;}{\levelnumbers\'01;}\fi-360\li1440\lin1440 }{\listname ;}\listid1}
{\list\listtemplateid2\listhybrid{\listlevel\levelnfc0\levelnfcn0\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace360\levelindent0{\*\levelmarker \{decimal\}.}{\leveltext\leveltemplateid101\'02\'00.;}{\levelnumbers\'01;}\fi-360\li720\lin720 }{\listlevel\levelnfc4\levelnfcn4\leveljc0\leveljcn0\levelfollow0\levelstartat1\levelspace360\levelindent0{\*\levelmarker \{lower-alpha\}.}{\leveltext\leveltemplateid102\'02\'01.;}{\levelnumbers\'01;}\fi-360\li1440\lin1440 }{\listname ;}\listid2}}
{\*\listoverridetable{\listoverride\listid1\listoverridecount0\ls1}{\listoverride\listid2\listoverridecount0\ls2}}
{\info
{\author Nathan Salomonis}
{\*\company CCHMC}}\margl1440\margr1440\vieww12240\viewh15840\viewkind1\viewscale97
\deftab720
\pard\pardeftab720\ri0\partightenfactor0

\f0\b\fs24 \cf0 Input Files for ICGS Analysis\
\pard\pardeftab720\ri0\partightenfactor0

\b0 \cf0 \
The latest version, ICGS version 2, includes improved methods to delineate more discrete populations on top of the original ICGS outputs and process extremely large datasets (>100,000 cells on a desktop computer). ICGS can process a diverse set of input file types as described here: \
\
https://github.com/nsalomonis/altanalyze/wiki/ICGS\
https://altanalyze.readthedocs.io/en/latest/Tutorial_De_Novo_SampleAnalysis/\
\
These files include 10x Genomics matrix files produced from the software Cell Ranger. The below steps outline the analysis steps required to process a 10x Genomics input file, such as the one provided in this directory.\
\pard\pardeftab720\li1440\ri0\partightenfactor0
\cf0 \
\pard\pardeftab720\ri0\partightenfactor0

\b \cf0 Running ICGS through the Graphical User Interface\
\
\pard\pardeftab720\li720\fi-360\ri0\partightenfactor0
\ls1\ilvl1
\b0 \cf0 1.	Double-click on the AltAnalyze executable (see Running-AltAnalyze file in the program directory for problems opening). \
2.	Install the correct species database when prompted.\
\pard\pardeftab720\li1440\fi-360\ri0\partightenfactor0
\ls1\ilvl1\cf0 a.	For the demo dataset, ensure Mus Musculus is downloaded.\
\pard\pardeftab720\li720\fi-360\ri0\partightenfactor0
\ls1\ilvl1\cf0 3.	From the main menu in AltAnalyze, select RNA-Seq as the \'93Select vendor/data type\'94. Select \'9310x Genomics aligned\'94 under \'93Select platform\'94. Then select the Continue button.\
4.	Select the \'93Process Chromium matrix\'94 radio button. Then select the Continue button.\
5.	
\b \ul Dataset Location
\b0 \ulnone : Enter a dataset name of choice (e.g., Kidney-e14). The with the \'93select file\'94 option, select the location of the provided .mtx file (DemoData/ICGS/10xGenomics/Mm-e14.5_Kidney-GSE104396/mm10/matrix.mtx). Select the output directory, which is the folder to save all results and subsequent input files to. Then select the Continue button.\
\pard\pardeftab720\li720\fi-360\ri0\partightenfactor0
\ls1\ilvl1
\b \cf0 6.	\ul Expression Analysis Parameters
\b0 \ulnone : Choose the additional options you want to include or exclude for the downstream pipeline analyses after ICGS (optional). These analyses will run after you select an specific ICGS output. Then select the Continue button. \
\ls1\ilvl1
\b 7.	\ul Select the ICGS Option Instead of Indicating Groups
\b0 \ulnone : In the Assign files to a Group Annotation\'94 menu, select the \'93Run de novo cluster prediction (ICGS) option at the top of the screen. The below options are used when assigning cell labels derived in advance for supervised group comparison (not applicable for unsupervised scRNA-Seq analysis).\
\ls1\ilvl1
\b 8.	\ul Modify the ICGS Parameters
\b0 \ulnone : A series of different options are available for ICGS, which include the option to include or exclude cell cycle effects (see the ICGS documentation). Is not necessary to modify these options, as ICGS2 iterates the initial steps of the analysis to identify the optimal correlation cutoff. When finished, select the \'93Run Analysis\'94 option.\
\ls1\ilvl1
\b 9.	\ul Analysis Progress
\b0 \ulnone : A black screen will appear once the analysis has begun. Be patient as the software is performing a series of in-depth analyses, which can take minutes to up-to an hour. Both tabular and graphical outputs will be produced in the input (query) folder in a new directories named ICGS and ICGS-NMF in the output directory. A log file will also be produced in the folder containing your input (.log). Any errors encountered will be reported in this fail. When complete, the software will pop-up an \'93Analysis Complete\'94 notification and will display the different ICGS iteration results. By default, the last option (ICGS-NMF) result is displayed which can be processed further to identify differential expressed genes between clusters, produce predicted regulatory networks, perform pathway and QC analyses, plus much more (\'93Use Selected\'94 option). \ul \
\pard\pardeftab720\ri0\partightenfactor0
\cf0 \ulnone \
\pard\pardeftab720\ri0\partightenfactor0

\b \cf0 Outputs of ICGS\
\pard\pardeftab720\ri0\partightenfactor0

\b0 \cf0 \
See the precomputed-results folder which contains examples. The final results of ICGS-NMF include:\
\pard\pardeftab720\li720\fi-360\ri0\partightenfactor0
\ls2\ilvl1
\b \cf0 1.	\ul Tabular
\b0 \ulnone  cell assignments to clusters in the FinalGroups text file.\
\ls2\ilvl1
\b 2.	\ul Tabular
\b0 \ulnone  marker genes per cluster MarkerGenes text file. 
\b \ul Heatmap image
\b0 \ulnone  file is saved for this file to FinalMarkerHeatmap.pdf .\
3.	If the file contained over 2,500 cells, a FinalMarkerHeatmap heat map will be saved for both the down-sampled dataset and all cells.\
\ls2\ilvl1
\b 4.	\ul UMAP image
\b0 \ulnone  file with all cells based on the ICGS-NMF reference genes.\
\ls2\ilvl1
\b 5.	\ul UMAP coordinate file
\b0 \ulnone  will be saved to this directory as well as to the folder DataPlots with a name corresponding to the newly reordered tab-delimited expression file for all analyzed cells (ExpressionInput folder). This expression file is named with the prefix \'93exp.\'94 and the suffix \'93-ICGS.txt\'94 with the user supplied name included. Users can produced customized UMAP image results with the expression of individual or groups of genes colored in the UMAP plot through the \'93Dimensionality Reduction\'94 menu (Additional Analysis menu, after the main menu).\
\
\pard\pardeftab720\ri0\partightenfactor0

\b \cf0 Running ICGS on the command-line\
\
\pard\pardeftab720\ri0\partightenfactor0

\b0 \cf0 Below is an example command similar to the above analysis:\
\pard\pardeftab560\slleading20\partightenfactor0

\f1 \cf2 \

\f2 python AltAnalyze.py --platform RNASeq --species Mm --column_method hopach --column_metric cosine --rho 0.2 --ExpressionCutoff 1 --FoldDiff 4 --SamplesDiffering 4 --restrictBy protein_coding --excludeCellCycle conservative --removeOutliers no --row_method hopach --ChromiumSparseMatrix /Users/<USER>/DemoData/ICGS/10xGenomics/Mm-e14.5_Kidney-GSE104396/mm10/matrix.mtx --output /DemoData/ICGS/10xGenomics/Mm-e14.5_Kidney-GSE104396/ --runICGS yes --expname "Kidney-E14"\
}